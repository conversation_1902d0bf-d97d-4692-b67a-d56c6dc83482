version: '3.8'

services:
  backend:
    image: backend:latest
    ports:
      - "8001:8001"
    environment:
      - STABILITY_API_KEY=${STABILITY_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LOG_LEVEL=DEBUG
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s

  frontend:
    image: frontend:latest
    ports:
      - "3000:80"
    depends_on:
      - backend
    volumes:
      - ./client/nginx.debug.conf:/etc/nginx/conf.d/default.conf
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
