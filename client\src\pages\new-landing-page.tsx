"use client"

import { useEffect } from "react"
import { EmmaNavBar } from "@/components/ui/emma-navbar"
import { SectionDivider } from "@/components/ui/section-divider"
import { SEOHead, SEO_CONFIGS } from "@/components/seo/SEOHead"
import { EmmaHomepageStructuredData } from "@/components/seo/StructuredData"
import { HomepageHiddenSEO } from "@/components/seo/HiddenSEOContent"

import {
  HeroSection,
  WhatIsEmmaSection,
  EmmaConceptSection,
  HumanIntegrationSection,
  PlatformShowcase,
  SolutionSection,
  MarketingToolsSteps,
  AgentShowcase,
  PlatformFeatures,
  ProofResults,
  FAQSection,
  FooterSection,
  FinalCTA
} from "@/components/landing-sections"

export default function NewLandingPage() {
  // Aggressively hide scrollbars and remove blue backgrounds
  useEffect(() => {
    const fixLandingPageStyles = () => {
      const container = document.querySelector('.landing-page-container');
      const html = document.documentElement;
      const body = document.body;
      const root = document.getElementById('root');

      // Force white backgrounds on all parent elements
      if (html) html.style.backgroundColor = 'white';
      if (body) body.style.backgroundColor = 'white';
      if (root) root.style.backgroundColor = 'white';

      // Force remove any blue theme colors
      const themeColorMeta = document.querySelector('meta[name="theme-color"]');
      if (themeColorMeta) {
        themeColorMeta.setAttribute('content', '#ffffff');
      }

      const msAppTileMeta = document.querySelector('meta[name="msapplication-TileColor"]');
      if (msAppTileMeta) {
        msAppTileMeta.setAttribute('content', '#ffffff');
      }

      if (container) {
        // Force hide scrollbars with JavaScript
        (container as HTMLElement).style.scrollbarWidth = 'none';
        (container as HTMLElement).style.msOverflowStyle = 'none';
        (container as HTMLElement).style.setProperty('-webkit-overflow-scrolling', 'touch');
        (container as HTMLElement).style.backgroundColor = 'white';

        // Add aggressive CSS rules
        const style = document.createElement('style');
        style.textContent = `
          html, body, #root, .landing-page-container {
            background-color: white !important;
            background-image: none !important;
            background: white !important;
          }
          .landing-page-container::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
            opacity: 0 !important;
            visibility: hidden !important;
          }
          .landing-page-container::-webkit-scrollbar-track {
            display: none !important;
            background: transparent !important;
          }
          .landing-page-container::-webkit-scrollbar-thumb {
            display: none !important;
            background: transparent !important;
          }
          .landing-page-container {
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
          }
        `;
        document.head.appendChild(style);
      }
    };

    fixLandingPageStyles();

    // Re-apply on window resize and load (Safari sometimes resets)
    window.addEventListener('resize', fixLandingPageStyles);
    window.addEventListener('load', fixLandingPageStyles);

    return () => {
      window.removeEventListener('resize', fixLandingPageStyles);
      window.removeEventListener('load', fixLandingPageStyles);
    };
  }, []);

  return (
    <div
      className="min-h-screen bg-white overflow-x-hidden landing-page-container no-scrollbar"
      style={{
        scrollbarWidth: 'none',
        msOverflowStyle: 'none',
        WebkitOverflowScrolling: 'touch'
      }}
    >
      {/* SEO Components */}
      <SEOHead {...SEO_CONFIGS.home} />
      <EmmaHomepageStructuredData />
      <HomepageHiddenSEO />

      <EmmaNavBar />

      <main>
        <HeroSection />

        {/* Product Hunt Badge */}
        <section className="py-8 bg-white">
          <div className="container mx-auto px-4 text-center">
            <a
              href="https://www.producthunt.com/products/emma-ai-app?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-emma&#0045;ai&#0045;private&#0045;beta&#0045;launch"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block transition-transform duration-300 hover:scale-105"
            >
              <img
                src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=989724&theme=light&t=1752076642419"
                alt="Emma ai Private beta Launch - Everyone Can Be a Great Marketer | Product Hunt"
                style={{ width: '250px', height: '54px' }}
                width="250"
                height="54"
              />
            </a>
          </div>
        </section>

        <WhatIsEmmaSection />
        <SectionDivider variant="wave" color="gradient" />

        <PlatformFeatures />
        <SectionDivider variant="wave" color="blue" />

        <EmmaConceptSection />
        <SectionDivider variant="wave" color="gradient" />

        <AgentShowcase />
        <SectionDivider variant="wave" color="gradient" />

        <PlatformShowcase />
        <SectionDivider variant="wave" color="blue" />

        <SolutionSection />
        <SectionDivider variant="wave" color="gradient" />

        <MarketingToolsSteps />
        <SectionDivider variant="wave" color="red" />

        <HumanIntegrationSection />
        <SectionDivider variant="wave" color="gradient" />

        <ProofResults />
        <SectionDivider variant="wave" color="gradient" />

        {/* <TestimonialsSection /> */}
        {/* <SectionDivider variant="wave" color="gradient" /> */}

        {/* FAQ Section - Mantener solo la FAQ normal sin elementos LLM visibles */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <FAQSection />
            </div>
          </div>
        </section>
        <SectionDivider variant="wave" color="gradient" />

        <FinalCTA />
      </main>

      <FooterSection />
    </div>
  )
}


