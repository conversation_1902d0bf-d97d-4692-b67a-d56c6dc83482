"""
Módulo de configuración simplificado para evitar problemas de dependencias.
"""
import logging
import os
from typing import List, Optional
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Determine the base directory of the backend
# This assumes config.py is in app/core/
BACKEND_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)

# Path to the .env file
dotenv_path = os.path.join(BACKEND_DIR, ".env")

# Función simple para cargar variables de entorno desde un archivo .env
def load_env_file(env_path: str) -> None:
    """Carga variables de entorno desde un archivo .env"""
    if not os.path.exists(env_path):
        return

    with open(env_path, 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            if '=' not in line:
                continue
            key, value = line.split('=', 1)
            # Forzar la sobrescritura de variables del sistema
            os.environ[key.strip()] = value.strip().strip('"\'')

# Cargar variables de entorno
try:
    load_env_file(dotenv_path)
    # Forzar DATABASE_URL para desarrollo
    os.environ['DATABASE_URL'] = 'sqlite:///./emma_studio.db'
except Exception as e:
    print(f"Error cargando .env: {e}")

# Clase simple para acceder a las configuraciones como un objeto
class Settings:
    PROJECT_NAME: str = "Emma Studio"
    API_V1_STR: str = "/api/v1"
    ENVIRONMENT: str = "development"
    HOST: str = "0.0.0.0"
    PORT: int = 8001  # Fixed to match frontend proxy configuration

    # CORS
    ALLOWED_ORIGINS: List[str] = ["*"]
    ALLOWED_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"] if os.getenv("ENVIRONMENT", "development") == "development" else ["GET", "POST", "OPTIONS"]
    ALLOWED_HEADERS: List[str] = ["*"] if os.getenv("ENVIRONMENT", "development") == "development" else [
        "Content-Type",
        "Authorization",
        "X-Request-ID",
        "X-API-Key",
        "Accept"
    ]

    # Security
    API_KEY_NAME: str = "X-API-Key"
    API_KEY: str = ""

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60

    # External Services - User's actual API keys
    GEMINI_API_KEY: str = "AIzaSyBAF5GT1Isn2TBL-s9tUsdKQDI57Y8uQ18"
    OPENAI_API_KEY: str = ""
    IDEOGRAM_API_KEY: str = "1rrDHIqxD4vl6tSucVKy6AIDtb_ZUnuOZ_stZOJXfGpAZE7UfyCuB6R9K_hENxWlp-su3uNDY6dC95-geYAO1g"
    STABILITY_API_KEY: str = ""
    STABILITY_API_URL: str = "https://api.stability.ai"
    ELEVENLABS_API_KEY: str = ""
    SERPER_API_KEY: str = "2187e03c0d1710eeaa3e3669daf6a4fcddc1b84cb"
    GOOGLE_PAGESPEED_API_KEY: str = "AIzaSyBAF5GT1Isn2TBL-s9tUsdKQDI57Y8uQ18"

    # Database
    DATABASE_URL: str = "sqlite:///./emma_studio.db"  # Default SQLite database

    # Redis
    REDIS_URL: Optional[str] = None
    ENABLE_TRACE_CACHE: bool = False
    TRACE_CACHE_TTL: int = 3600  # 1 hour in seconds

    # Logging
    LOG_LEVEL: str = "INFO"

    def __init__(self):
        # Cargar valores desde variables de entorno
        self.PROJECT_NAME = os.getenv("PROJECT_NAME", self.PROJECT_NAME)
        self.API_V1_STR = os.getenv("API_V1_STR", self.API_V1_STR)
        self.ENVIRONMENT = os.getenv("ENVIRONMENT", self.ENVIRONMENT)
        self.HOST = os.getenv("HOST", self.HOST)
        self.PORT = int(os.getenv("PORT", str(self.PORT)))
        self.GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", self.GEMINI_API_KEY)
        self.OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", self.OPENAI_API_KEY)
        self.IDEOGRAM_API_KEY = os.getenv("IDEOGRAM_API_KEY", self.IDEOGRAM_API_KEY)
        self.STABILITY_API_KEY = os.getenv("STABILITY_API_KEY", self.STABILITY_API_KEY)
        self.STABILITY_API_URL = os.getenv("STABILITY_API_URL", self.STABILITY_API_URL)
        self.ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY", self.ELEVENLABS_API_KEY)
        self.SERPER_API_KEY = os.getenv("SERPER_API_KEY", self.SERPER_API_KEY)
        self.GOOGLE_PAGESPEED_API_KEY = os.getenv("GOOGLE_PAGESPEED_API_KEY", self.GOOGLE_PAGESPEED_API_KEY)
        self.DATABASE_URL = os.getenv("DATABASE_URL", self.DATABASE_URL)
        self.REDIS_URL = os.getenv("REDIS_URL", self.REDIS_URL)
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", self.LOG_LEVEL)

settings = Settings()

def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings

# Configuración profesional de logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
                    handlers=[logging.StreamHandler()])
logger = logging.getLogger(__name__)
logger.info("Logging configurado correctamente")

# Configurar FastAPI
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    description="Emma Studio Intelligence API - Real SEO and AI Content Generation"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.info("CORS configurado correctamente para las siguientes URLs: %s", settings.ALLOWED_ORIGINS)

# Include SEO Intelligence API routes
try:
    from ..api.seo_intelligence import router as seo_router
    app.include_router(seo_router)
    logger.info("SEO Intelligence API routes loaded successfully")
except ImportError as e:
    logger.warning(f"Could not load SEO Intelligence routes: {e}")


# ============================================================================
# AGENT SYSTEM CONFIGURATION
# ============================================================================

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any


class AgentType(Enum):
    """Enumeration of available agent types."""
    EMMA = "emma"
    SEO = "seo"
    CONTENT = "content"


class TaskPriorityLevel(Enum):
    """Task priority levels."""
    LOW = 1
    MEDIUM = 3
    HIGH = 5
    CRITICAL = 7


class WorkflowConstants:
    """Constants for workflow execution."""
    MAX_WORKFLOW_STEPS = 10
    DEFAULT_TIMEOUT_SECONDS = 300
    MAX_RETRY_ATTEMPTS = 3
    CHUNK_SIZE_DIVISOR = 5
    MIN_CHUNK_SIZE = 1


class ValidationConstants:
    """Constants for input validation."""
    MIN_PROMPT_LENGTH = 3
    MAX_PROMPT_LENGTH = 5000
    MIN_AGENT_ID_LENGTH = 2
    MAX_AGENT_ID_LENGTH = 50
    AGENT_ID_PATTERN = r'^[a-zA-Z0-9_-]+$'


class LoggingConstants:
    """Constants for logging configuration."""
    MAX_LOG_MESSAGE_LENGTH = 1000
    API_KEY_PREVIEW_LENGTH = 5
    TRACE_RETENTION_HOURS = 24


@dataclass
class AgentServiceSettings:
    """Settings for the agent service."""
    gemini_api_key: Optional[str] = None
    openai_api_key: Optional[str] = None
    enable_reasoning_tracking: bool = True
    enable_streaming: bool = True
    max_concurrent_tasks: int = 5
    default_task_timeout: int = WorkflowConstants.DEFAULT_TIMEOUT_SECONDS

    def __post_init__(self):
        """Load settings from environment variables."""
        self.gemini_api_key = os.environ.get("GEMINI_API_KEY")
        self.openai_api_key = os.environ.get("OPENAI_API_KEY")

    @property
    def has_valid_api_key(self) -> bool:
        """Check if at least one valid API key is configured."""
        return bool(self.gemini_api_key or self.openai_api_key)

    @property
    def preferred_provider(self) -> str:
        """Get the preferred LLM provider."""
        if self.gemini_api_key:
            return "gemini"
        elif self.openai_api_key:
            return "openai"
        else:
            return "fallback"


def get_agent_metadata(agent_type: AgentType) -> Dict[str, Any]:
    """Get metadata for a specific agent type."""
    metadata = {
        AgentType.EMMA: {
            "id": "emma",
            "name": "Emma",
            "role": "Coordinator",
            "description": "I coordinate our team of specialized agents to help with your requests and ensure everything runs smoothly.",
            "specialties": ["coordination", "team management", "task delegation", "workflow optimization"],
            "color": "#4F46E5",
            "icon": "👩‍💼"
        },
        AgentType.SEO: {
            "id": "seo",
            "name": "SEO Specialist",
            "role": "Analyst",
            "description": "I analyze and optimize content for search engines to improve visibility and drive organic traffic.",
            "specialties": ["keyword research", "content optimization", "SEO analysis", "search trends"],
            "color": "#059669",
            "icon": "📈"
        },
        AgentType.CONTENT: {
            "id": "content",
            "name": "Content Creator",
            "role": "Creator",
            "description": "I create engaging, high-quality content for various platforms tailored to your specific audience and goals.",
            "specialties": ["content creation", "copywriting", "editing", "storytelling", "brand voice"],
            "color": "#DC2626",
            "icon": "✍️"
        }
    }
    return metadata.get(agent_type, {})


def get_all_agent_metadata() -> List[Dict[str, Any]]:
    """Get metadata for all available agents."""
    return [get_agent_metadata(agent_type) for agent_type in AgentType]


# Global agent service settings
agent_settings = AgentServiceSettings()