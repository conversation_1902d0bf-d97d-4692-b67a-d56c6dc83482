<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend-Backend Connection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔍 Test Frontend-Backend Connection</h1>
    
    <button onclick="testDirectBackend()">Test Direct Backend (8000)</button>
    <button onclick="testProxyConnection()">Test Proxy Connection (3002)</button>
    <button onclick="testFreeGeneration()">Test Free Generation Endpoint</button>
    <button onclick="testPostGeneration()">Test Post Generation</button>
    
    <div id="results"></div>

    <script>
        function addResult(title, success, message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test ${success ? 'success' : 'error'}`;
            div.innerHTML = `<h3>${title}</h3><p>${message}</p>`;
            results.appendChild(div);
        }

        async function testDirectBackend() {
            try {
                const response = await fetch('http://localhost:8001/api/v1/posts/info');  // TODO EL BACKEND CORRE EN PUERTO 8001
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Direct Backend', true, `Connected! Service: ${data.service}`);
                } else {
                    addResult('❌ Direct Backend', false, `HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('❌ Direct Backend', false, error.message);
            }
        }

        async function testProxyConnection() {
            try {
                const response = await fetch('/api/v1/posts/info');
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Proxy Connection', true, `Proxy works! Service: ${data.service}`);
                } else {
                    addResult('❌ Proxy Connection', false, `HTTP ${response.status}`);
                }
            } catch (error) {
                addResult('❌ Proxy Connection', false, error.message);
            }
        }

        async function testFreeGeneration() {
            try {
                const formData = new FormData();
                formData.append('prompt', 'test product');
                formData.append('platform', 'instagram');
                formData.append('size', '1024x1024');
                formData.append('num_images', '1');
                formData.append('use_product_image', 'false');

                const response = await fetch('/api/v1/ad-creator-agent/free-generation', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Free Generation', true, `Success! Generated: ${data.num_generated || 'unknown'} images`);
                } else {
                    const errorText = await response.text();
                    addResult('❌ Free Generation', false, `HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                addResult('❌ Free Generation', false, error.message);
            }
        }

        async function testPostGeneration() {
            try {
                const testData = {
                    brandInfo: {
                        businessName: "Test Business",
                        brandColor: "#3018ef",
                        voice: "profesional",
                        topics: ["test post"],
                        ctas: ["Test CTA"],
                        industry: "test"
                    },
                    designConfig: {
                        selectedTheme: "Balance",
                        platform: "Instagram",
                        contentType: "educational"
                    },
                    generationConfig: {
                        count: 1,
                        template: "Educational",
                        analysisComplete: true
                    }
                };

                const response = await fetch('/api/v1/posts/generate-batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ Post Generation', true, `Success! Generated: ${data.posts?.length || 0} posts`);
                } else {
                    const errorText = await response.text();
                    addResult('❌ Post Generation', false, `HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                addResult('❌ Post Generation', false, error.message);
            }
        }

        // Auto-run tests on load
        window.onload = function() {
            setTimeout(testDirectBackend, 500);
            setTimeout(testProxyConnection, 1000);
        };
    </script>
</body>
</html>
