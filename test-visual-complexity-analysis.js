/**
 * Test script to analyze the Visual Complexity Analyzer image handling issue
 * This will help us understand why images are not being saved with file_url
 */

console.log('🔍 Testing Visual Complexity Analyzer Image Handling...');

// Function to create a test image file
function createTestImageFile() {
  // Create a simple canvas with some content
  const canvas = document.createElement('canvas');
  canvas.width = 400;
  canvas.height = 300;
  const ctx = canvas.getContext('2d');
  
  // Draw a simple test design
  ctx.fillStyle = '#3498db';
  ctx.fillRect(0, 0, 400, 300);
  
  ctx.fillStyle = '#ffffff';
  ctx.font = '24px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('Test Design', 200, 150);
  
  ctx.fillStyle = '#e74c3c';
  ctx.fillRect(50, 50, 100, 100);
  
  ctx.fillStyle = '#2ecc71';
  ctx.fillRect(250, 50, 100, 100);
  
  // Convert canvas to blob and create File object
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      const file = new File([blob], 'test-design.png', { type: 'image/png' });
      resolve(file);
    }, 'image/png');
  });
}

// Function to test the backend analysis endpoint
async function testAnalysisEndpoint() {
  try {
    console.log('📤 Creating test image file...');
    const testFile = await createTestImageFile();
    console.log('✅ Test file created:', {
      name: testFile.name,
      size: testFile.size,
      type: testFile.type
    });

    console.log('🔐 Getting authentication token...');
    // Get current session from Supabase
    const { supabase } = await import('/src/lib/supabase.js');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.error('❌ No valid session found:', sessionError);
      console.log('ℹ️ Please log in to test authenticated analysis');
      return;
    }

    console.log('✅ Session found:', {
      userId: session.user.id,
      hasToken: !!session.access_token
    });

    console.log('📤 Sending analysis request to backend...');
    const formData = new FormData();
    formData.append('design', testFile);

    const response = await fetch('/api/analyze-design', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`
      },
      body: formData
    });

    console.log('📥 Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Backend error:', errorText);
      return;
    }

    const analysisData = await response.json();
    console.log('✅ Analysis completed:', {
      success: analysisData.success,
      savedToDatabase: analysisData.saved_to_database,
      analysisId: analysisData.analysis_id,
      hasFileUrl: !!analysisData.file_url,
      fileUrl: analysisData.file_url
    });

    // If analysis was saved, check the database record
    if (analysisData.analysis_id) {
      console.log('🔍 Checking database record...');
      
      const { data: dbRecord, error: dbError } = await supabase
        .schema('api')
        .from('design_analyses')
        .select('id, original_filename, file_url, overall_score, created_at')
        .eq('id', analysisData.analysis_id)
        .single();

      if (dbError) {
        console.error('❌ Database query error:', dbError);
      } else {
        console.log('📊 Database record:', {
          id: dbRecord.id,
          filename: dbRecord.original_filename,
          hasFileUrl: !!dbRecord.file_url,
          fileUrl: dbRecord.file_url,
          score: dbRecord.overall_score,
          createdAt: dbRecord.created_at
        });

        // Test image retrieval if file_url exists
        if (dbRecord.file_url) {
          console.log('🖼️ Testing image retrieval...');
          try {
            const { designAnalysisService } = await import('/src/services/designAnalysisService.js');
            const imageUrl = await designAnalysisService.getImageUrl(dbRecord.file_url);
            
            if (imageUrl) {
              console.log('✅ Image retrieval successful:', {
                type: imageUrl.startsWith('blob:') ? 'Object URL' : 'HTTP URL',
                url: imageUrl.substring(0, 50) + '...'
              });
              
              // Test if image actually loads
              const img = new Image();
              img.onload = () => console.log('✅ Image loads successfully in browser');
              img.onerror = (e) => console.error('❌ Image failed to load:', e);
              img.src = imageUrl;
              
            } else {
              console.error('❌ Failed to retrieve image URL');
            }
          } catch (retrievalError) {
            console.error('❌ Image retrieval error:', retrievalError);
          }
        } else {
          console.warn('⚠️ No file_url in database - this is the main issue!');
        }
      }
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Function to check recent analyses in the database
async function checkRecentAnalyses() {
  try {
    console.log('📊 Checking recent analyses in database...');
    
    const { supabase } = await import('/src/lib/supabase.js');
    const { data: analyses, error } = await supabase
      .schema('api')
      .from('design_analyses')
      .select('id, created_at, original_filename, file_url, overall_score, user_id')
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      console.error('❌ Database query error:', error);
      return;
    }

    console.log(`📋 Found ${analyses.length} recent analyses:`);
    analyses.forEach((analysis, index) => {
      console.log(`${index + 1}. ${analysis.original_filename}`, {
        id: analysis.id,
        hasFileUrl: !!analysis.file_url,
        fileUrl: analysis.file_url,
        score: analysis.overall_score,
        userId: analysis.user_id,
        createdAt: analysis.created_at
      });
    });

    // Count analyses with and without file_url
    const withFileUrl = analyses.filter(a => a.file_url).length;
    const withoutFileUrl = analyses.filter(a => !a.file_url).length;
    
    console.log('📈 Summary:', {
      total: analyses.length,
      withImages: withFileUrl,
      withoutImages: withoutFileUrl,
      imageSuccessRate: `${((withFileUrl / analyses.length) * 100).toFixed(1)}%`
    });

  } catch (error) {
    console.error('💥 Database check failed:', error);
  }
}

// Run the tests
async function runTests() {
  console.log('🚀 Starting Visual Complexity Analyzer tests...\n');
  
  await checkRecentAnalyses();
  console.log('\n' + '='.repeat(50) + '\n');
  await testAnalysisEndpoint();
  
  console.log('\n✅ Tests completed. Check the logs above for issues.');
}

// Auto-run if this script is loaded directly
if (typeof window !== 'undefined') {
  runTests();
}
