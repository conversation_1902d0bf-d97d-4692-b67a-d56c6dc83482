"""
Test Frontend Integration

This script tests the integration between the frontend and the new backend agent system.
It simulates frontend API calls to verify that the backend endpoints are working correctly.
"""

import os
import asyncio
import logging
import json
import httpx
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API base URL - TODO EL BACKEND CORRE EN PUERTO 8001
API_BASE_URL = "http://localhost:8001"


async def test_crew_run_endpoint():
    """Test the /api/v1/crew/run endpoint."""
    logger.info("Testing /api/v1/crew/run endpoint")
    
    # Create request payload
    payload = {
        "crew_id": "test-crew",
        "prompt": "Create a marketing campaign for a new smartphone",
        "inputs": {
            "product_name": "SuperPhone X",
            "target_audience": "tech enthusiasts",
            "key_features": ["AI capabilities", "long battery life", "high-quality camera"]
        }
    }
    
    # Send request to the endpoint
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{API_BASE_URL}/api/v1/crew/run",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            # Check response
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Crew run successful: {json.dumps(result, indent=2)}")
                return result
            else:
                logger.error(f"Crew run failed with status code {response.status_code}: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error testing crew run endpoint: {e}")
            return None


async def test_agent_chat_endpoint():
    """Test the /api/v1/crew/chat endpoint."""
    logger.info("Testing /api/v1/crew/chat endpoint")
    
    # Create request payload
    payload = {
        "agent_id": "emma",
        "message": "How can I create an effective social media campaign?",
        "context": {
            "industry": "technology",
            "platform": "instagram"
        }
    }
    
    # Send request to the endpoint
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{API_BASE_URL}/api/v1/crew/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            # Check response
            if response.status_code == 200:
                result = response.json()
                logger.info(f"Agent chat successful: {json.dumps(result, indent=2)}")
                return result
            else:
                logger.error(f"Agent chat failed with status code {response.status_code}: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error testing agent chat endpoint: {e}")
            return None


async def main():
    """Run all tests."""
    logger.info("Starting frontend integration tests")
    
    # Test crew run endpoint
    crew_result = await test_crew_run_endpoint()
    
    # Test agent chat endpoint
    chat_result = await test_agent_chat_endpoint()
    
    # Print summary
    logger.info("Frontend integration tests completed")
    logger.info(f"Crew run test: {'SUCCESS' if crew_result else 'FAILED'}")
    logger.info(f"Agent chat test: {'SUCCESS' if chat_result else 'FAILED'}")


if __name__ == "__main__":
    asyncio.run(main())
