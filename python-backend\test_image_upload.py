#!/usr/bin/env python3
"""
Test script to verify Visual Complexity Analyzer image upload and storage
"""

import asyncio
import requests
import json
from io import BytesIO
from PIL import Image

async def test_image_upload():
    """Test the complete image upload and analysis flow"""
    
    print("🧪 Testing Visual Complexity Analyzer Image Upload Flow")
    print("=" * 60)
    
    # Create a test image
    print("📸 Creating test image...")
    img = Image.new('RGB', (400, 300), color='red')
    img_buffer = BytesIO()
    img.save(img_buffer, format='PNG')
    img_buffer.seek(0)
    
    # Prepare the request
    files = {
        'design': ('test_image.png', img_buffer.getvalue(), 'image/png')
    }
    
    # You'll need to get a valid JWT token from the frontend
    # For now, let's test without authentication to see the flow
    headers = {
        # 'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE'
    }
    
    print("🚀 Sending request to backend...")
    
    try:
        response = requests.post(
            'http://localhost:8001/api/design-analysis/analyze',  # TODO EL BACKEND CORRE EN PUERTO 8001
            files=files,
            headers=headers,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print(f"📊 Analysis ID: {result.get('analysis_id', 'NOT SET')}")
            print(f"📊 Saved to Database: {result.get('saved_to_database', 'NOT SET')}")
            print(f"📊 Score: {result.get('score', 'NOT SET')}")
            
            # Check if analysis was saved and has file_url
            if result.get('analysis_id'):
                print(f"\n🔍 Checking database record for analysis: {result['analysis_id']}")
                # This would require a database query - we'll check manually
                
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"📊 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request error: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_image_upload())
