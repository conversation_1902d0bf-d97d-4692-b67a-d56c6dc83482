/**
 * Debug script to analyze mood board image persistence in the database
 * Run this script to check if images are being saved correctly
 */

console.log('🔍 Mood Board Image Persistence Debug');
console.log('=====================================\n');

async function debugMoodBoardImages() {
  try {
    // Step 1: Check if we can access the backend
    console.log('📡 Step 1: Testing backend connectivity...');

    const healthResponse = await fetch('http://localhost:5001/health');
    if (healthResponse.ok) {
      console.log('✅ Backend is accessible');
    } else {
      console.log('❌ Backend health check failed');
      return;
    }

    // Step 2: Get authentication token (if available)
    console.log('\n🔐 Step 2: Checking authentication...');

    const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    if (!token) {
      console.log('⚠️ No authentication token found. Please log in first.');
      return;
    }
    console.log('✅ Authentication token found');

    // Step 3: Fetch mood boards
    console.log('\n📋 Step 3: Fetching mood boards...');

    const moodboardsResponse = await fetch('http://localhost:5001/api/moodboard/list', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!moodboardsResponse.ok) {
      console.log('❌ Failed to fetch mood boards:', moodboardsResponse.status);
      return;
    }

    const moodboardsData = await moodboardsResponse.json();
    const moodboards = moodboardsData.data || [];

    console.log(`✅ Found ${moodboards.length} mood boards`);

    if (moodboards.length === 0) {
      console.log('ℹ️ No mood boards found. Create a mood board with images to test.');
      return;
    }

    // Step 4: Analyze each mood board for image content
    console.log('\n🖼️ Step 4: Analyzing mood boards for image content...');

    for (const [index, moodboard] of moodboards.entries()) {
      console.log(`\n--- Mood Board ${index + 1}: "${moodboard.title}" ---`);
      console.log(`ID: ${moodboard.id}`);
      console.log(`Created: ${new Date(moodboard.created_at).toLocaleString()}`);
      console.log(`Updated: ${new Date(moodboard.updated_at).toLocaleString()}`);

      // Check if tldraw_data exists
      if (!moodboard.tldraw_data) {
        console.log('⚠️ No tldraw_data found');
        continue;
      }

      console.log('✅ tldraw_data exists');

      // Analyze the tldraw_data structure
      const tldrawData = moodboard.tldraw_data;
      const dataSize = JSON.stringify(tldrawData).length;
      console.log(`📏 Data size: ${(dataSize / 1024).toFixed(2)} KB`);

      // Look for image shapes
      let imageCount = 0;
      let totalImageSize = 0;
      const imageFormats = new Set();

      if (tldrawData.store) {
        for (const [shapeId, shape] of Object.entries(tldrawData.store)) {
          if (shape.type === 'image' && shape.props?.src) {
            imageCount++;
            const src = shape.props.src;
            const imageSize = src.length;
            totalImageSize += imageSize;

            // Detect image format
            if (src.startsWith('data:image/')) {
              const format = src.split(';')[0].split('/')[1];
              imageFormats.add(format.toUpperCase());
            }

            console.log(`  🖼️ Image ${imageCount}:`);
            console.log(`    - Shape ID: ${shapeId}`);
            console.log(`    - Size: ${(imageSize / 1024).toFixed(2)} KB`);
            console.log(`    - Position: (${shape.x || 0}, ${shape.y || 0})`);
            console.log(`    - Dimensions: ${shape.props.w || 'unknown'} x ${shape.props.h || 'unknown'}`);

            // Check if image data is valid
            if (src.startsWith('data:image/')) {
              console.log(`    - ✅ Valid data URL format`);
            } else if (src.startsWith('http')) {
              console.log(`    - ✅ External URL format`);
            } else {
              console.log(`    - ⚠️ Unknown image format`);
            }
          }
        }
      }

      console.log(`📊 Summary:`);
      console.log(`  - Total images: ${imageCount}`);
      console.log(`  - Total image data: ${(totalImageSize / 1024).toFixed(2)} KB`);
      console.log(`  - Image formats: ${Array.from(imageFormats).join(', ') || 'None'}`);

      if (imageCount === 0) {
        console.log('ℹ️ No images found in this mood board');
      } else {
        console.log('✅ Images are properly stored in tldraw_data');
      }
    }

    // Step 5: Test image loading
    console.log('\n🔄 Step 5: Testing image loading...');

    const moodboardWithImages = moodboards.find(mb => {
      if (!mb.tldraw_data?.store) return false;
      return Object.values(mb.tldraw_data.store).some(shape =>
        shape.type === 'image' && shape.props?.src
      );
    });

    if (moodboardWithImages) {
      console.log(`Testing with mood board: "${moodboardWithImages.title}"`);

      // Try to load the mood board
      const loadResponse = await fetch(`http://localhost:5001/api/moodboard/${moodboardWithImages.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (loadResponse.ok) {
        const loadedData = await loadResponse.json();
        console.log('✅ Mood board loaded successfully');

        if (loadedData.data?.tldraw_data) {
          console.log('✅ tldraw_data preserved in loaded mood board');

          // Count images in loaded data
          const loadedImages = Object.values(loadedData.data.tldraw_data.store || {})
            .filter(shape => shape.type === 'image' && shape.props?.src);

          console.log(`✅ ${loadedImages.length} images found in loaded data`);
        } else {
          console.log('❌ tldraw_data missing in loaded mood board');
        }
      } else {
        console.log('❌ Failed to load mood board for testing');
      }
    } else {
      console.log('ℹ️ No mood boards with images found for loading test');
    }

    console.log('\n🎯 Debug Summary:');
    console.log('=================');
    console.log('✅ Backend connectivity: OK');
    console.log('✅ Authentication: OK');
    console.log(`✅ Mood boards found: ${moodboards.length}`);

    const totalImages = moodboards.reduce((count, mb) => {
      if (!mb.tldraw_data?.store) return count;
      return count + Object.values(mb.tldraw_data.store).filter(shape =>
        shape.type === 'image' && shape.props?.src
      ).length;
    }, 0);

    console.log(`✅ Total images across all mood boards: ${totalImages}`);

    if (totalImages > 0) {
      console.log('\n💡 Image persistence is working correctly!');
      console.log('   - Images are embedded in tldraw_data as expected');
      console.log('   - Data is properly stored and retrieved from database');
      console.log('   - JSONB storage is handling the image data correctly');
    } else {
      console.log('\n⚠️ No images found in any mood boards');
      console.log('   - Create a mood board and add some images to test persistence');
      console.log('   - Try different image formats (PNG, JPEG, WebP)');
      console.log('   - Test with different image sizes');
    }

  } catch (error) {
    console.error('❌ Debug script failed:', error);
  }
}

// Run the debug script
debugMoodBoardImages();