import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Brain,
  ArrowLeft,
  ArrowRight,
  Upload,
  Globe,
  Palette,
  Users,
  MessageSquare,
  Target,
  FileText,
  Sparkles,
  Building,
  Image,
  Link,
  CheckCircle,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { useLocation } from "wouter";
import { MarcaService } from "@/services/marca-service";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { validateImageFile, validateDocumentFile } from "@/lib/utils/file-validation";
import { extractColorsFromImage, type ExtractedColor } from "@/lib/utils/color-extraction";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const CrearMarcaPage = () => {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [extractedColors, setExtractedColors] = useState<ExtractedColor[]>([]);
  const [isExtractingColors, setIsExtractingColors] = useState(false);
  const logoInputRef = useRef<HTMLInputElement>(null);
  const totalSteps = 5;

  // Estado del formulario con personality selection mejorada
  const [formData, setFormData] = useState({
    // Paso 1: Información básica
    brandName: "",
    website: "",
    industry: "",

    // Paso 2: Identidad visual
    logo: null as File | null,
    primaryColor: "#3018ef",
    secondaryColor: "#dd3a5a",

    // Paso 3: Audiencia y tono
    targetAudience: "",
    tone: "",
    personality: [] as string[], // Array for predefined traits
    customPersonality: "", // Custom traits input

    // Paso 4: Descripción y posicionamiento
    description: "",
    uniqueValue: "",
    competitors: "",

    // Paso 5: Ejemplos (removed complex document handling)
    examples: ""
  });

  // Predefined personality traits
  const personalityTraits = [
    "Confiable", "Innovadora", "Accesible", "Premium",
    "Sostenible", "Global", "Local", "Disruptiva",
    "Tradicional", "Moderna", "Experta", "Inclusiva",
    "Amigable", "Profesional", "Creativa", "Juvenil"
  ];

  // State for "Otro" option
  const [showCustomPersonality, setShowCustomPersonality] = useState(false);

  // Helper functions for personality management
  const togglePersonalityTrait = (trait: string) => {
    setFormData(prev => ({
      ...prev,
      personality: prev.personality.includes(trait)
        ? prev.personality.filter(p => p !== trait)
        : [...prev.personality, trait]
    }));
  };

  const handleCustomPersonalityToggle = () => {
    setShowCustomPersonality(!showCustomPersonality);
    if (showCustomPersonality) {
      // If hiding custom input, clear custom personality
      setFormData(prev => ({ ...prev, customPersonality: "" }));
    }
  };

  const getAllPersonalityTraits = () => {
    const allTraits = [...formData.personality];
    if (formData.customPersonality.trim()) {
      // Split custom traits by comma and add them
      const customTraits = formData.customPersonality
        .split(',')
        .map(trait => trait.trim())
        .filter(trait => trait.length > 0);
      allTraits.push(...customTraits);
    }
    return allTraits;
  };

  // Handle logo upload
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    // Clean up previous preview if it exists
    if (logoPreview && logoPreview.startsWith('blob:')) {
      URL.revokeObjectURL(logoPreview);
    }

    // Update form data
    setFormData({ ...formData, logo: file });

    // Create preview using URL.createObjectURL for better performance
    const previewUrl = URL.createObjectURL(file);
    setLogoPreview(previewUrl);

    // Extract colors from the uploaded image
    await extractColorsFromLogo(file);
  };

  // Extract colors from logo
  const extractColorsFromLogo = async (file: File) => {
    setIsExtractingColors(true);

    try {
      toast({
        title: "Extrayendo colores",
        description: "Analizando los colores de tu logo...",
      });

      const result = await extractColorsFromImage(file, 5);

      if (result.success && result.colors.length > 0) {
        setExtractedColors(result.colors);

        // Auto-update primary and secondary colors with the most dominant colors
        const primaryColor = result.colors[0]?.hex || formData.primaryColor;
        const secondaryColor = result.colors[1]?.hex || formData.secondaryColor;

        setFormData(prev => ({
          ...prev,
          primaryColor,
          secondaryColor
        }));

        toast({
          title: "¡Colores extraídos!",
          description: `Se encontraron ${result.colors.length} colores dominantes en tu logo`,
        });
      } else {
        toast({
          title: "No se pudieron extraer colores",
          description: result.error || "Intenta con una imagen diferente",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error extracting colors:", error);
      toast({
        title: "Error",
        description: "No se pudieron extraer los colores del logo",
        variant: "destructive",
      });
    } finally {
      setIsExtractingColors(false);
    }
  };

  // Simplified: No complex document handling needed for basic brand creation

  // Trigger file inputs
  const triggerLogoUpload = () => {
    logoInputRef.current?.click();
  };

  // Removed document upload functionality

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    const file = files[0];
    if (file && file.type.startsWith('image/')) {
      const validation = validateImageFile(file);
      if (!validation.valid) {
        toast({
          title: "Archivo inválido",
          description: validation.error,
          variant: "destructive",
        });
        return;
      }

      // Clean up previous preview if it exists
      if (logoPreview && logoPreview.startsWith('blob:')) {
        URL.revokeObjectURL(logoPreview);
      }

      // Update form data
      setFormData({ ...formData, logo: file });

      // Create preview
      const previewUrl = URL.createObjectURL(file);
      setLogoPreview(previewUrl);

      // Extract colors from the dropped image
      await extractColorsFromLogo(file);
    }
  };

  // Cleanup preview URL on unmount
  useEffect(() => {
    return () => {
      if (logoPreview && logoPreview.startsWith('blob:')) {
        URL.revokeObjectURL(logoPreview);
      }
    };
  }, [logoPreview]);

  const steps = [
    { id: 1, title: "Información Básica", icon: Building },
    { id: 2, title: "Identidad Visual", icon: Palette },
    { id: 3, title: "Audiencia & Tono", icon: Users },
    { id: 4, title: "Posicionamiento", icon: Target },
    { id: 5, title: "Documentos", icon: FileText }
  ];

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // Verificar autenticación
      if (!user) {
        toast({
          title: "Error de autenticación",
          description: "Debes iniciar sesión para crear una marca",
          variant: "destructive"
        });
        setLoading(false);
        navigate("/login");
        return;
      }

      // Validar campos requeridos
      if (!formData.brandName || !formData.industry || !formData.targetAudience ||
          !formData.tone || !formData.description || !formData.uniqueValue) {
        toast({
          title: "Error",
          description: "Por favor completa todos los campos requeridos",
          variant: "destructive"
        });
        setLoading(false);
        return;
      }

      // Validar que se haya seleccionado al menos una característica de personalidad
      if (getAllPersonalityTraits().length === 0) {
        toast({
          title: "Error",
          description: "Por favor selecciona al menos una característica de personalidad para tu marca",
          variant: "destructive"
        });
        setLoading(false);
        return;
      }

      // Crear el marca en Supabase con datos simplificados
      const marcaData = {
        brand_name: formData.brandName,
        website: formData.website || '',
        industry: formData.industry,
        logo_url: formData.logo ? formData.logo.name : undefined, // Simple file name storage
        primary_color: formData.primaryColor,
        secondary_color: formData.secondaryColor,
        target_audience: formData.targetAudience,
        tone: formData.tone,
        personality: getAllPersonalityTraits(), // Use combined predefined and custom traits
        description: formData.description,
        unique_value: formData.uniqueValue,
        competitors: formData.competitors || '',
        documents: [], // Simplified: no complex document handling
        examples: formData.examples || '',
        user_id: user?.id || undefined,
      };

      console.log('Submitting marca data:', marcaData);

      const result = await MarcaService.createMarca(marcaData);

      console.log('Marca created successfully:', result);

      toast({
        title: "¡Marca creada exitosamente!",
        description: `${result.brand_name} ha sido creada y está lista para usar`,
        action: (
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => navigate('/dashboard/marca')}
            >
              Ver Dashboard
            </Button>
          </div>
        )
      });

      // Navigate to the newly created brand's detail page
      navigate(`/dashboard/marca/${result.id}`);
    } catch (error) {
      console.error('Error creating marca:', error);

      let errorMessage = "No se pudo crear la marca. Inténtalo de nuevo.";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Información Básica de tu Marca
              </h2>
              <p className="text-gray-600">
                Empecemos con los datos fundamentales de tu marca
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="brandName" className="text-base font-medium">
                  Nombre de la Marca *
                </Label>
                <Input
                  id="brandName"
                  placeholder="Ej: Emma Studio"
                  value={formData.brandName}
                  onChange={(e) => setFormData({...formData, brandName: e.target.value})}
                  className="h-12 text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website" className="text-base font-medium">
                  Sitio Web
                </Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="website"
                    placeholder="https://emma.ai"
                    value={formData.website}
                    onChange={(e) => setFormData({...formData, website: e.target.value})}
                    className="h-12 pl-10 text-base"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="industry" className="text-base font-medium">
                Industria *
              </Label>
              <Select onValueChange={(value) => setFormData({...formData, industry: value})}>
                <SelectTrigger className="h-12 text-base">
                  <SelectValue placeholder="Selecciona tu industria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="saas">SaaS & Tecnología</SelectItem>
                  <SelectItem value="ecommerce">E-commerce & Retail</SelectItem>
                  <SelectItem value="servicios">Servicios Profesionales</SelectItem>
                  <SelectItem value="salud">Salud & Bienestar</SelectItem>
                  <SelectItem value="educacion">Educación & Formación</SelectItem>
                  <SelectItem value="finanzas">Finanzas & Seguros</SelectItem>
                  <SelectItem value="inmobiliaria">Inmobiliaria</SelectItem>
                  <SelectItem value="alimentacion">Alimentación & Bebidas</SelectItem>
                  <SelectItem value="entretenimiento">Entretenimiento & Media</SelectItem>
                  <SelectItem value="otro">Otro</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Identidad Visual
              </h2>
              <p className="text-gray-600">
                Define los elementos visuales que representan tu marca
              </p>
            </div>

            {/* Logo Upload */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Logo de la Marca</Label>
              <div
                className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 transition-colors bg-gray-50 cursor-pointer"
                onClick={triggerLogoUpload}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <input
                  ref={logoInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />

                {logoPreview ? (
                  <div className="space-y-4">
                    <div className="relative inline-block">
                      <img
                        src={logoPreview}
                        alt="Logo preview"
                        className="max-h-32 mx-auto rounded-lg shadow-sm"
                      />
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-green-600 flex items-center justify-center gap-2">
                        <CheckCircle className="w-4 h-4" />
                        Logo cargado exitosamente
                      </p>
                      <Button variant="outline" size="sm">
                        Cambiar logo
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Sube tu logo
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Arrastra tu archivo aquí o haz clic para seleccionar
                    </p>
                    <Button variant="outline" className="mb-2">
                      Seleccionar archivo
                    </Button>
                    <p className="text-sm text-gray-500">
                      PNG, JPG, WebP hasta 30MB
                    </p>
                  </>
                )}
              </div>
            </div>

            {/* Extracted Colors Display */}
            {(isExtractingColors || extractedColors.length > 0) && (
              <div className="space-y-4">
                <Label className="text-base font-medium">Colores Extraídos del Logo</Label>

                {isExtractingColors ? (
                  <div className="flex items-center justify-center p-6 border-2 border-dashed border-blue-300 rounded-xl bg-blue-50">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                      <p className="text-sm text-blue-600">Extrayendo colores del logo...</p>
                    </div>
                  </div>
                ) : extractedColors.length > 0 ? (
                  <div className="space-y-3">
                    <div className="flex flex-wrap gap-2">
                      {extractedColors.map((color, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 p-2 border rounded-lg bg-white hover:shadow-sm transition-shadow cursor-pointer"
                          onClick={() => {
                            if (index === 0) {
                              setFormData(prev => ({ ...prev, primaryColor: color.hex }));
                            } else if (index === 1) {
                              setFormData(prev => ({ ...prev, secondaryColor: color.hex }));
                            }
                            toast({
                              title: "Color aplicado",
                              description: `${color.name} aplicado como color ${index === 0 ? 'primario' : 'secundario'}`,
                            });
                          }}
                        >
                          <div
                            className="w-6 h-6 rounded-full border-2 border-gray-200"
                            style={{ backgroundColor: color.hex }}
                          ></div>
                          <div className="text-sm">
                            <div className="font-medium">{color.name}</div>
                            <div className="text-gray-500 text-xs">{color.hex}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <p className="text-xs text-gray-500">
                      💡 Puedes copiar el color o seleccionar uno dando click al texto o a la imagen del color
                    </p>
                  </div>
                ) : null}
              </div>
            )}

            {/* Colores */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label className="text-base font-medium">Color Primario</Label>
                  {extractedColors.length > 0 && extractedColors[0]?.hex === formData.primaryColor && (
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                      Extraído del logo
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-lg border-2 border-gray-200"
                    style={{ backgroundColor: formData.primaryColor }}
                  ></div>
                  <Input
                    type="color"
                    value={formData.primaryColor}
                    onChange={(e) => setFormData({...formData, primaryColor: e.target.value})}
                    className="w-20 h-12"
                  />
                  <Input
                    value={formData.primaryColor}
                    onChange={(e) => setFormData({...formData, primaryColor: e.target.value})}
                    placeholder="#3018ef"
                    className="flex-1 h-12"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label className="text-base font-medium">Color Secundario</Label>
                  {extractedColors.length > 1 && extractedColors[1]?.hex === formData.secondaryColor && (
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                      Extraído del logo
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <div
                    className="w-12 h-12 rounded-lg border-2 border-gray-200"
                    style={{ backgroundColor: formData.secondaryColor }}
                  ></div>
                  <Input
                    type="color"
                    value={formData.secondaryColor}
                    onChange={(e) => setFormData({...formData, secondaryColor: e.target.value})}
                    className="w-20 h-12"
                  />
                  <Input
                    value={formData.secondaryColor}
                    onChange={(e) => setFormData({...formData, secondaryColor: e.target.value})}
                    placeholder="#dd3a5a"
                    className="flex-1 h-12"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Audiencia y Personalidad
              </h2>
              <p className="text-gray-600">
                Define a quién te diriges y cómo quieres comunicarte
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="targetAudience" className="text-base font-medium">
                  Público Objetivo *
                </Label>
                <Textarea
                  id="targetAudience"
                  placeholder="Describe tu audiencia principal: edad, profesión, intereses, necesidades..."
                  value={formData.targetAudience}
                  onChange={(e) => setFormData({...formData, targetAudience: e.target.value})}
                  rows={4}
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tone" className="text-base font-medium">
                  Tono de Voz *
                </Label>
                <Select onValueChange={(value) => setFormData({...formData, tone: value})}>
                  <SelectTrigger className="h-12 text-base">
                    <SelectValue placeholder="¿Cómo quieres que suene tu marca?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="profesional">Profesional y Autoritativo</SelectItem>
                    <SelectItem value="amigable">Amigable y Cercano</SelectItem>
                    <SelectItem value="innovador">Innovador y Visionario</SelectItem>
                    <SelectItem value="casual">Casual y Relajado</SelectItem>
                    <SelectItem value="elegante">Elegante y Sofisticado</SelectItem>
                    <SelectItem value="divertido">Divertido y Energético</SelectItem>
                    <SelectItem value="tecnico">Técnico y Preciso</SelectItem>
                    <SelectItem value="inspiracional">Inspiracional y Motivador</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label className="text-base font-medium">
                  Personalidad de la Marca
                </Label>
                <p className="text-sm text-gray-600">
                  Selecciona las características que mejor describen tu marca
                </p>

                {/* Predefined personality traits grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {personalityTraits.map((trait) => (
                    <Button
                      key={trait}
                      type="button"
                      variant={formData.personality.includes(trait) ? "default" : "outline"}
                      onClick={() => togglePersonalityTrait(trait)}
                      className="h-10 text-sm justify-start"
                    >
                      {trait}
                    </Button>
                  ))}

                  {/* "Otro" option */}
                  <Button
                    type="button"
                    variant={showCustomPersonality ? "default" : "outline"}
                    onClick={handleCustomPersonalityToggle}
                    className="h-10 text-sm justify-start"
                  >
                    Otro
                  </Button>
                </div>

                {/* Custom personality input (shown when "Otro" is selected) */}
                {showCustomPersonality && (
                  <div className="space-y-2">
                    <Label htmlFor="customPersonality" className="text-sm font-medium">
                      Características personalizadas
                    </Label>
                    <Textarea
                      id="customPersonality"
                      placeholder="Ej: Vanguardista, Ecológica, Minimalista..."
                      value={formData.customPersonality}
                      onChange={(e) => setFormData({...formData, customPersonality: e.target.value})}
                      rows={2}
                      className="text-base"
                    />
                    <p className="text-xs text-gray-500">
                      Separa las características personalizadas con comas
                    </p>
                  </div>
                )}

                {/* Selected traits preview */}
                {getAllPersonalityTraits().length > 0 && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">
                      Características seleccionadas:
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {getAllPersonalityTraits().map((trait, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {trait}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Posicionamiento y Diferenciación
              </h2>
              <p className="text-gray-600">
                Define qué hace única a tu marca y cómo se posiciona
              </p>
            </div>

            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="description" className="text-base font-medium">
                  Descripción de tu Marca *
                </Label>
                <Textarea
                  id="description"
                  placeholder="Describe qué hace tu marca, qué problema resuelve y cómo ayuda a tus clientes..."
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={4}
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="uniqueValue" className="text-base font-medium">
                  Propuesta de Valor Única *
                </Label>
                <Textarea
                  id="uniqueValue"
                  placeholder="¿Qué te diferencia de la competencia? ¿Por qué los clientes deberían elegirte?"
                  value={formData.uniqueValue}
                  onChange={(e) => setFormData({...formData, uniqueValue: e.target.value})}
                  rows={3}
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="competitors" className="text-base font-medium">
                  Principales Competidores
                </Label>
                <Input
                  id="competitors"
                  placeholder="Ej: Nike, Adidas, Puma..."
                  value={formData.competitors}
                  onChange={(e) => setFormData({...formData, competitors: e.target.value})}
                  className="h-12 text-base"
                />
                <p className="text-sm text-gray-500">
                  Esto ayuda a Emma a diferenciarte en el mercado
                </p>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Lineamientos de Contenido
              </h2>
              <p className="text-gray-600">
                Define cómo quieres que Emma cree contenido para tu marca
              </p>
            </div>

            <div className="space-y-6">

              {/* Lineamientos de contenido */}
              <div className="space-y-2">
                <Label htmlFor="examples" className="text-base font-medium">
                  Lineamientos de Contenido y Referencias
                </Label>
                <Textarea
                  id="examples"
                  placeholder="Describe cómo te gustaría que sea el marketing de tu marca: amigable y sereno como Nike en Instagram, profesional y confiable como Apple, divertido y juvenil como Coca-Cola, etc. Incluye lineamientos específicos y recomendaciones que quieres que la IA siga al crear contenido para tu marca."
                  value={formData.examples}
                  onChange={(e) => setFormData({...formData, examples: e.target.value})}
                  rows={5}
                  className="text-base"
                />
                <p className="text-sm text-gray-500">
                  Esto ayuda a Emma a crear contenido que refleje perfectamente tu estilo y personalidad de marca
                </p>
              </div>

              {/* Resumen final */}
              <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  ¡Casi listo!
                </h3>
                <p className="text-blue-800 mb-4">
                  Emma procesará toda esta información para crear tu Marca inteligente.
                  Podrás editarlo y mejorarlo en cualquier momento.
                </p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Identidad definida</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Audiencia clara</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Tono establecido</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>Posicionamiento único</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Paso {currentStep}</div>;
    }
  };

  return (
    <DashboardLayout pageTitle="Crear Marca">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30">
        {/* Header */}
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate("/dashboard/marca")}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Marca
          </Button>
          
          <div className="flex items-center gap-4 mb-6">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <Brain className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Crear Nuevo Marca
              </h1>
              <p className="text-gray-600">
                Enseña a Emma quién es tu marca para que ejecute con contexto total
              </p>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-8">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <step.icon className="h-5 w-5" />
                  )}
                </div>
                <div className="ml-3 hidden md:block">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-4 ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <Card className="max-w-4xl mx-auto border-0 shadow-xl bg-white/80 backdrop-blur-sm">
          <CardContent className="p-8">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStep()}
            </motion.div>

            {/* Navigation */}
            <div className="flex justify-between mt-8 pt-6 border-t">
              <Button
                variant="outline"
                onClick={handlePrev}
                disabled={currentStep === 1}
                className="px-6"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Anterior
              </Button>

              {currentStep === totalSteps ? (
                <Button
                  onClick={handleSubmit}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creando...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Crear Marca
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6"
                >
                  Siguiente
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default CrearMarcaPage;
