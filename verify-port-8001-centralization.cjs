/**
 * Script de Verificación - Centralización Puerto 8001
 * 
 * Este script verifica que toda la configuración de Emma Studio
 * esté correctamente centralizada en el puerto 8001 para el backend.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 VERIFICACIÓN DE CENTRALIZACIÓN - PUERTO 8001');
console.log('='.repeat(50));

// Archivos críticos que deben estar configurados correctamente
const criticalFiles = [
  {
    file: 'backend/app/core/config.py',
    check: (content) => content.includes('PORT: int = 8001'),
    description: 'Backend configurado para puerto 8001'
  },
  {
    file: 'client/vite.config.ts',
    check: (content) => content.includes('http://127.0.0.1:8001'),
    description: 'Frontend proxy configurado para puerto 8001'
  },
  {
    file: 'start_backend.bat',
    check: (content) => content.includes('--port 8001'),
    description: 'Script de inicio configurado para puerto 8001'
  }
];

// Archivos de testing que deben usar puerto 8001
const testFiles = [
  'test-mood-board-api-endpoints.js',
  'debug-mood-board-creation.js',
  'final-mood-board-test.js',
  'test-connection.js',
  'backend/test_frontend_integration.py',
  'backend/test_full_flow.py',
  'backend/test_workflows.py'
];

let allGood = true;

console.log('\n📋 VERIFICANDO ARCHIVOS CRÍTICOS...\n');

// Verificar archivos críticos
criticalFiles.forEach(({ file, check, description }) => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (check(content)) {
        console.log(`✅ ${description}`);
      } else {
        console.log(`❌ ${description} - CONFIGURACIÓN INCORRECTA`);
        allGood = false;
      }
    } else {
      console.log(`⚠️  ${file} - ARCHIVO NO ENCONTRADO`);
      allGood = false;
    }
  } catch (error) {
    console.log(`❌ Error verificando ${file}: ${error.message}`);
    allGood = false;
  }
});

console.log('\n📋 VERIFICANDO ARCHIVOS DE TESTING...\n');

// Verificar archivos de testing
testFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('localhost:8001') || content.includes('127.0.0.1:8001')) {
        console.log(`✅ ${file} - Configurado para puerto 8001`);
      } else if (content.includes('localhost:8000') || content.includes('127.0.0.1:8000')) {
        console.log(`❌ ${file} - TODAVÍA USA PUERTO 8000`);
        allGood = false;
      } else {
        console.log(`⚠️  ${file} - No contiene URLs de localhost`);
      }
    } else {
      console.log(`⚠️  ${file} - ARCHIVO NO ENCONTRADO`);
    }
  } catch (error) {
    console.log(`❌ Error verificando ${file}: ${error.message}`);
  }
});

console.log('\n📋 VERIFICANDO ARCHIVOS OBSOLETOS...\n');

// Verificar que archivos obsoletos hayan sido eliminados
const obsoleteFiles = ['vite.config.js'];
obsoleteFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`❌ ${file} - ARCHIVO OBSOLETO TODAVÍA EXISTE`);
    allGood = false;
  } else {
    console.log(`✅ ${file} - Archivo obsoleto eliminado correctamente`);
  }
});

console.log('\n📋 VERIFICANDO DOCUMENTACIÓN...\n');

// Verificar documentación actualizada
const docFiles = [
  'README.md',
  'PUERTO_8001_CENTRALIZACION_COMPLETA.md',
  'docs/RUNTIME_GUIDE.md',
  'backend/README.md'
];

docFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('8001')) {
        console.log(`✅ ${file} - Documentación actualizada para puerto 8001`);
      } else {
        console.log(`⚠️  ${file} - Documentación podría necesitar actualización`);
      }
    } else {
      console.log(`⚠️  ${file} - ARCHIVO NO ENCONTRADO`);
    }
  } catch (error) {
    console.log(`❌ Error verificando ${file}: ${error.message}`);
  }
});

console.log('\n' + '='.repeat(50));

if (allGood) {
  console.log('🎉 ¡VERIFICACIÓN EXITOSA!');
  console.log('✅ Toda la configuración está centralizada en puerto 8001');
  console.log('✅ No se encontraron inconsistencias');
  console.log('✅ Emma Studio está listo para funcionar de manera confiable');
  
  console.log('\n🚀 COMANDOS PARA INICIAR:');
  console.log('Backend:  cd backend && python -m uvicorn app.main:app --reload --port 8001');
  console.log('Frontend: cd client && npm run dev');
  console.log('URLs:     Frontend: http://localhost:3002 | Backend: http://localhost:8001');
} else {
  console.log('❌ VERIFICACIÓN FALLÓ');
  console.log('⚠️  Se encontraron inconsistencias en la configuración');
  console.log('🔧 Revisar los archivos marcados con ❌ y corregir la configuración');
}

console.log('\n📊 RESUMEN DE PUERTOS:');
console.log('🎯 Backend:  Puerto 8001 (TODO EL BACKEND)');
console.log('🎯 Frontend: Puerto 3002');
console.log('🎯 Proxy:    /api → http://127.0.0.1:8001');

console.log('\n📚 Para más información:');
console.log('📖 Ver: PUERTO_8001_CENTRALIZACION_COMPLETA.md');
console.log('📖 Ver: CONEXION_ARREGLADA.md');
