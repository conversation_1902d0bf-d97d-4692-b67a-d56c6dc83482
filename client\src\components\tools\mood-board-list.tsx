import { useState, useEffect, useMemo } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Edit3,
  Trash2,
  Calendar,
  MoreVertical,
  Search,
  Filter,
  Loader2,
  FileText,
  Save,
  X
} from "lucide-react"
import { useLocation } from "wouter"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useMoodboard } from "@/hooks/use-moodboard"
import { useAuth } from "@/hooks/use-auth"
import { useToast } from "@/hooks/use-toast"
import type { Moodboard } from "@/lib/supabase"

export default function MoodBoardList() {
  const [, setLocation] = useLocation()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")
  const { user, isLoading: isAuthLoading } = useAuth()
  const { toast } = useToast()

  // Description editing modal state
  const [isDescriptionModalOpen, setIsDescriptionModalOpen] = useState(false)
  const [editingMoodboard, setEditingMoodboard] = useState<Moodboard | null>(null)
  const [editingDescription, setEditingDescription] = useState("")
  const [isSavingDescription, setIsSavingDescription] = useState(false)

  // Delete confirmation modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deletingMoodboard, setDeletingMoodboard] = useState<Moodboard | null>(null)

  // Use the moodboard hook for data management
  const {
    moodboardList,
    isLoadingList,
    listError,
    deleteMoodboard,
    isDeleting,
    refetchList,
    updateMoodboard
  } = useMoodboard()

  // Filter moodboards based on search and filter criteria with error handling
  const filteredMoodBoards = useMemo(() => {
    try {
      if (!moodboardList || !Array.isArray(moodboardList)) {
        console.warn('moodboardList is not an array:', moodboardList)
        return []
      }

      return moodboardList.filter(board => {
        if (!board || typeof board !== 'object') {
          console.warn('Invalid board object:', board)
          return false
        }

        const title = board.title || ""
        const description = board.description || ""

        const matchesSearch = title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             description.toLowerCase().includes(searchTerm.toLowerCase())

        if (selectedFilter === "all") return matchesSearch
        if (selectedFilter === "favorite") return matchesSearch && board.is_favorite

        return matchesSearch
      })
    } catch (error) {
      console.error('Error filtering moodboards:', error)
      return []
    }
  }, [moodboardList, searchTerm, selectedFilter])

  const handleCreateNew = async () => {
    // Navigate to editor with 'new' ID - the editor will handle creation
    setLocation(`/dashboard/herramientas/mood-board/editor/new`)
  }

  const handleOpenBoard = (boardId: string) => {
    setLocation(`/dashboard/herramientas/mood-board/editor/${boardId}`)
  }

  const handleDeleteBoard = (moodboard: Moodboard) => {
    setDeletingMoodboard(moodboard)
    setIsDeleteModalOpen(true)
  }

  const confirmDeleteBoard = async () => {
    if (!deletingMoodboard) return

    console.log('🗑️ Starting delete operation for:', deletingMoodboard.id)

    try {
      await deleteMoodboard(deletingMoodboard.id)

      console.log('✅ Delete operation completed successfully')

      // Show success toast since hook no longer handles it
      toast({
        title: "Mood Board eliminado",
        description: "El mood board ha sido eliminado exitosamente.",
      })

      // Close modal and reset state immediately
      setIsDeleteModalOpen(false)
      setDeletingMoodboard(null)

      console.log('✅ Modal state reset after successful delete')
    } catch (error) {
      console.error('❌ Error deleting moodboard:', error)

      // Show error toast
      toast({
        title: "Error al eliminar",
        description: error instanceof Error ? error.message : "Ocurrió un error inesperado al eliminar el mood board.",
        variant: "destructive",
      })

      // Reset modal state even on error to prevent UI freeze
      setIsDeleteModalOpen(false)
      setDeletingMoodboard(null)

      console.log('🔄 Modal state reset after delete error')
    }
  }

  const cancelDeleteBoard = () => {
    setIsDeleteModalOpen(false)
    setDeletingMoodboard(null)
  }

  const handleEditDescription = (moodboard: Moodboard) => {
    setEditingMoodboard(moodboard)
    setEditingDescription(moodboard.description || "")
    setIsDescriptionModalOpen(true)
  }

  const handleSaveDescription = async () => {
    if (!editingMoodboard) return

    setIsSavingDescription(true)
    try {
      await updateMoodboard({
        id: editingMoodboard.id,
        data: {
          description: editingDescription.trim()
        }
      })

      // Show success toast since hook handles it conditionally
      toast({
        title: "Descripción actualizada",
        description: "La descripción del mood board ha sido actualizada exitosamente.",
      })

      // Close modal and reset state immediately
      setIsDescriptionModalOpen(false)
      setEditingMoodboard(null)
      setEditingDescription("")
    } catch (error) {
      console.error('Error updating description:', error)

      // Show error toast
      toast({
        title: "Error al actualizar descripción",
        description: error instanceof Error ? error.message : "Ocurrió un error inesperado.",
        variant: "destructive",
      })

      // Reset modal state even on error to prevent UI freeze
      setIsDescriptionModalOpen(false)
      setEditingMoodboard(null)
      setEditingDescription("")
    } finally {
      setIsSavingDescription(false)
    }
  }

  const handleCancelEditDescription = () => {
    setIsDescriptionModalOpen(false)
    setEditingMoodboard(null)
    setEditingDescription("")
  }

  // Cleanup function to reset all modal states on component unmount
  useEffect(() => {
    return () => {
      // Reset all modal states on unmount to prevent memory leaks
      setIsDescriptionModalOpen(false)
      setEditingMoodboard(null)
      setEditingDescription("")
      setIsSavingDescription(false)
      setIsDeleteModalOpen(false)
      setDeletingMoodboard(null)
    }
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Show loading state while authenticating or loading data
  if (isAuthLoading || isLoadingList) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center h-64"
        >
          <div className="text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 mx-auto mb-4"
            >
              <Loader2 className="w-full h-full text-indigo-600" />
            </motion.div>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-gray-600 text-lg"
            >
              {isAuthLoading ? "Verificando autenticación..." : "Cargando tus mood boards..."}
            </motion.p>
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: "100%" }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              className="h-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full mt-4 max-w-xs mx-auto"
            />
          </div>
        </motion.div>
      </div>
    )
  }

  // Show error state if there's an error loading data
  if (listError) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error al cargar los mood boards</p>
            <Button onClick={() => refetchList()} variant="outline">
              Reintentar
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Show authentication required state
  if (!user) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-gray-600 mb-4">Debes iniciar sesión para ver tus mood boards</p>
            <Button onClick={() => setLocation('/login')} className="bg-gradient-to-r from-indigo-600 to-purple-600">
              Iniciar Sesión
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl relative">
      {/* Global Loading Overlay */}
      {(isDeleting || isSavingDescription) && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 shadow-xl flex items-center space-x-3">
            <Loader2 className="w-6 h-6 animate-spin text-indigo-600" />
            <span className="text-gray-700 font-medium">
              {isDeleting ? "Eliminando mood board..." : "Guardando descripción..."}
            </span>
          </div>
        </div>
      )}

      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 text-transparent bg-clip-text">
              Mis Mood Boards
            </h1>
            <p className="text-gray-600 mt-2 text-lg">
              Gestiona y crea tableros de inspiración visual para tus proyectos
            </p>
          </div>

          <Button
            onClick={handleCreateNew}
            size="lg"
            disabled={isDeleting || isSavingDescription}
            className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-5 h-5 mr-2" />
            Crear Nuevo
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Buscar mood boards..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="w-4 h-4" />
                Filtrar
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setSelectedFilter("all")}>
                Todos
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedFilter("favorite")}>
                Favoritos
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoadingList ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
            className="w-10 h-10 mx-auto mb-4"
          >
            <div className="w-full h-full border-4 border-indigo-200 border-t-indigo-600 rounded-full"></div>
          </motion.div>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-gray-600 text-lg font-medium"
          >
            Actualizando mood boards...
          </motion.p>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="text-sm text-gray-400 mt-2"
          >
            Esto solo tomará un momento
          </motion.div>
        </motion.div>
      ) : (
        <>
          {/* Mood Boards Grid - Improved Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredMoodBoards.map((board, index) => (
          <motion.div
            key={board.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            whileHover={{ y: -6, scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            className="group"
          >
            <Card className={`relative overflow-hidden border-0 shadow-lg hover:shadow-2xl hover:shadow-indigo-500/20 transition-all duration-700 cursor-pointer bg-white/80 backdrop-blur-sm ${
              (isDeleting || isSavingDescription) ? 'pointer-events-none opacity-60' : ''
            }`}>
              {/* Preview Image Container */}
              <div className="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                {/* Canvas Preview */}
                {board.canvas_snapshot ? (
                  <img
                    src={board.canvas_snapshot}
                    alt={board.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-48 bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-indigo-200 rounded-full flex items-center justify-center mb-2">
                        <Edit3 className="w-8 h-8 text-indigo-600" />
                      </div>
                      <p className="text-indigo-600 font-medium">Mood Board</p>
                    </div>
                  </div>
                )}

                <motion.div
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 flex items-center justify-center gap-3"
                >
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    whileHover={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    <Button
                      size="sm"
                      onClick={() => handleOpenBoard(board.id)}
                      className="bg-white text-gray-800 hover:bg-white/90 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <Edit3 className="w-4 h-4 mr-1" />
                      Editar
                    </Button>
                  </motion.div>
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    whileHover={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditDescription(board)}
                      disabled={isDeleting || isSavingDescription}
                      className="bg-white/95 text-gray-800 hover:bg-white border-white/50 shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FileText className="w-4 h-4 mr-1" />
                      Descripción
                    </Button>
                  </motion.div>
                </motion.div>


              </div>
              
              <CardContent className="p-5">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-bold text-xl text-gray-800 line-clamp-1 group-hover:text-indigo-700 transition-colors duration-300">{board.title}</h3>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        disabled={isDeleting}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem
                        onClick={() => handleOpenBoard(board.id)}
                        disabled={isDeleting || isSavingDescription}
                      >
                        <Edit3 className="w-4 h-4 mr-2" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteBoard(board)}
                        disabled={isDeleting || isSavingDescription}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        {isDeleting ? "Eliminando..." : "Eliminar"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <p className="text-gray-600 text-base mb-4 line-clamp-2 leading-relaxed">
                  {board.description || "Sin descripción disponible"}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {board.tags.map((tag, tagIndex) => (
                    <motion.div
                      key={tag}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.1 * tagIndex }}
                    >
                      <Badge
                        variant="outline"
                        className="text-xs px-2 py-1 bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100 transition-colors duration-200"
                      >
                        {tag}
                      </Badge>
                    </motion.div>
                  ))}
                </div>

                {/* Date and Stats */}
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="w-3 h-3 mr-1" />
                  Actualizado {formatDate(board.updated_at)}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
          </div>

          {/* Empty State */}
          {filteredMoodBoards.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center py-16"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center"
              >
                <motion.div
                  animate={{ rotate: [0, 5, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                >
                  <Plus className="w-16 h-16 text-indigo-500" />
                </motion.div>
              </motion.div>

              <motion.h3
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-2xl font-bold text-gray-800 mb-3"
              >
                {searchTerm ? "No se encontraron resultados" : "¡Comienza tu creatividad!"}
              </motion.h3>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-gray-600 mb-8 max-w-md mx-auto text-lg"
              >
                {searchTerm
                  ? `No hay mood boards que coincidan con "${searchTerm}". Intenta con otros términos de búsqueda.`
                  : "Crea tu primer mood board para organizar ideas visuales, colores e inspiración para tus proyectos."
                }
              </motion.p>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8 }}
              >
                <Button
                  onClick={handleCreateNew}
                  size="lg"
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  {searchTerm ? "Crear Nuevo Mood Board" : "Crear Mi Primer Mood Board"}
                </Button>
              </motion.div>

              {!searchTerm && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2 }}
                  className="mt-8 text-sm text-gray-500"
                >
                  <p>💡 Los mood boards te ayudan a:</p>
                  <div className="flex justify-center gap-6 mt-2 text-xs">
                    <span>🎨 Organizar ideas visuales</span>
                    <span>🌈 Explorar paletas de colores</span>
                    <span>✨ Capturar inspiración</span>
                  </div>
                </motion.div>
              )}
            </motion.div>
          )}
        </>
      )}

      {/* Description Editing Modal */}
      <Dialog open={isDescriptionModalOpen} onOpenChange={setIsDescriptionModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Descripción</DialogTitle>
            <DialogDescription>
              Actualiza la descripción de "{editingMoodboard?.title}"
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="description" className="text-sm font-medium">
                Descripción
              </label>
              <Textarea
                id="description"
                placeholder="Escribe una descripción para tu mood board..."
                value={editingDescription}
                onChange={(e) => setEditingDescription(e.target.value)}
                rows={4}
                className="resize-none"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleCancelEditDescription}
              disabled={isSavingDescription}
            >
              <X className="w-4 h-4 mr-2" />
              Cancelar
            </Button>
            <Button
              onClick={handleSaveDescription}
              disabled={isSavingDescription}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
            >
              {isSavingDescription ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isSavingDescription ? "Guardando..." : "Guardar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Confirmar Eliminación</DialogTitle>
            <DialogDescription>
              ¿Estás seguro de que deseas eliminar "{deletingMoodboard?.title}"?
              Esta acción no se puede deshacer.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={cancelDeleteBoard}
              disabled={isDeleting}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteBoard}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4 mr-2" />
              )}
              {isDeleting ? "Eliminando..." : "Eliminar"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
