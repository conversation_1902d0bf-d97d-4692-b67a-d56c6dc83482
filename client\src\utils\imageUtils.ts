/**
 * Image utility functions for download and URL handling
 */

export const downloadImage = async (imageUrl: string, filename: string = 'emma-ad') => {
  console.log('📥 Starting download for:', imageUrl);

  try {
    // Extraer la URL original de Ideogram si es una URL proxy
    let originalUrl = imageUrl;
    if (imageUrl.includes('/proxy-image?url=')) {
      const urlMatch = imageUrl.match(/url=([^&]+)/);
      originalUrl = urlMatch ? decodeURIComponent(urlMatch[1]) : imageUrl;
    }

    // Usar el endpoint de descarga específico para free generation
    const downloadUrl = `/api/v1/ad-creator-agent/download-image?url=${encodeURIComponent(originalUrl)}`;

    console.log('🔄 Using backend download endpoint:', downloadUrl);

    // Crear enlace de descarga directo
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('✅ Download initiated successfully');
    return { success: true, method: 'backend' };

  } catch (error) {
    console.error('❌ Download failed:', error);
    return { success: false, error: 'No se pudo descargar la imagen. Inténtalo de nuevo.' };
  }
};

export const copyImageUrl = async (imageUrl: string): Promise<{ success: boolean; error?: string }> => {
  try {
    await navigator.clipboard.writeText(imageUrl);
    return { success: true };
  } catch (error) {
    console.error('Error copying URL:', error);
    return { success: false, error: 'Failed to copy URL' };
  }
};
