# Guía de despliegue y conexión frontend-backend – Vibe Marketing

## 1. Variables de entorno para local y producción

- **Local:**
  - Edita `/client/.env`:
    ```
    VITE_API_URL=http://localhost:8001
    ```
- **Producción:**
  - Define la variable `VITE_API_URL` en el panel de tu plataforma de despliegue (Netlify, Vercel, etc.) con la URL pública del backend:
    ```
    VITE_API_URL=https://tu-backend-vibe-marketing.com
    ```

## 2. Consideraciones para CORS
- El backend debe permitir solicitudes CORS desde el dominio donde se despliegue el frontend.
- Ejemplo para FastAPI:
  ```python
  from fastapi.middleware.cors import CORSMiddleware
  app.add_middleware(
      CORSMiddleware,
      allow_origins=["*"],  # O especifica tu dominio
      allow_credentials=True,
      allow_methods=["*"],
      allow_headers=["*"],
  )
  ```

## 3. Proxy en desarrollo (opcional)
- Si usas Vite, puedes añadir un proxy en `vite.config.ts` para facilitar el desarrollo local:
  ```js
  server: {
    proxy: {
      '/api': 'http://localhost:8000',
    },
  },
  ```

## 4. Checklist para despliegue seguro
- [ ] Variable `VITE_API_URL` correcta según entorno
- [ ] Backend accesible públicamente
- [ ] CORS configurado en backend
- [ ] Frontend sin referencias a `localhost` en producción
- [ ] Probar flujos de agentes tras despliegue

---

**¡Con esto tu app funcionará tanto en local como en producción!**
