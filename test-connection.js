#!/usr/bin/env node

/**
 * Script de prueba para verificar la conexión entre frontend y backend
 * Ejecutar con: node test-connection.js
 */

const http = require('http');

// Configuración - TODO EL BACKEND CORRE EN PUERTO 8001
const BACKEND_HOST = '127.0.0.1';
const BACKEND_PORT = 8001;  // TODO EL BACKEND DE EMMA STUDIO CORRE EN PUERTO 8001
const FRONTEND_PORT = 3002;

console.log('🔍 Verificando conexión Frontend-Backend...\n');

// Test 1: Verificar que el backend esté corriendo
function testBackendHealth() {
  return new Promise((resolve, reject) => {
    console.log('1️⃣ Verificando salud del backend...');
    
    const options = {
      hostname: BACKEND_HOST,
      port: BACKEND_PORT,
      path: '/api/v1/health',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Backend está corriendo correctamente');
          console.log(`   Respuesta: ${data}\n`);
          resolve(true);
        } else {
          console.log(`❌ Backend respondió con código: ${res.statusCode}`);
          console.log(`   Respuesta: ${data}\n`);
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Error conectando al backend: ${err.message}`);
      console.log(`   ¿Está el backend corriendo en puerto ${BACKEND_PORT}?\n`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('❌ Timeout conectando al backend\n');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Test 2: Verificar endpoints de agentes
function testAgentsEndpoint() {
  return new Promise((resolve, reject) => {
    console.log('2️⃣ Verificando endpoint de agentes...');
    
    const options = {
      hostname: BACKEND_HOST,
      port: BACKEND_PORT,
      path: '/api/v1/crew/agents',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Endpoint de agentes funciona correctamente');
          console.log(`   Respuesta: ${data.substring(0, 100)}...\n`);
          resolve(true);
        } else {
          console.log(`❌ Endpoint de agentes falló con código: ${res.statusCode}`);
          console.log(`   Respuesta: ${data}\n`);
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Error en endpoint de agentes: ${err.message}\n`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('❌ Timeout en endpoint de agentes\n');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Test 3: Verificar endpoint de chat
function testChatEndpoint() {
  return new Promise((resolve, reject) => {
    console.log('3️⃣ Verificando endpoint de chat...');
    
    const postData = JSON.stringify({
      agent_id: 'emma',
      message: 'hola',
      context: {}
    });

    const options = {
      hostname: BACKEND_HOST,
      port: BACKEND_PORT,
      path: '/api/v1/crew/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'X-Request-ID': `test_${Date.now()}`
      },
      timeout: 10000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Endpoint de chat funciona correctamente');
          console.log(`   Respuesta: ${data.substring(0, 100)}...\n`);
          resolve(true);
        } else {
          console.log(`❌ Endpoint de chat falló con código: ${res.statusCode}`);
          console.log(`   Respuesta: ${data}\n`);
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Error en endpoint de chat: ${err.message}\n`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('❌ Timeout en endpoint de chat\n');
      req.destroy();
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

// Test 4: Verificar CORS
function testCORS() {
  return new Promise((resolve, reject) => {
    console.log('4️⃣ Verificando configuración CORS...');
    
    const options = {
      hostname: BACKEND_HOST,
      port: BACKEND_PORT,
      path: '/api/v1/health',
      method: 'OPTIONS',
      headers: {
        'Origin': `http://localhost:${FRONTEND_PORT}`,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      },
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      const corsHeaders = {
        'Access-Control-Allow-Origin': res.headers['access-control-allow-origin'],
        'Access-Control-Allow-Methods': res.headers['access-control-allow-methods'],
        'Access-Control-Allow-Headers': res.headers['access-control-allow-headers']
      };

      if (res.statusCode === 200 || res.statusCode === 204) {
        console.log('✅ CORS configurado correctamente');
        console.log(`   Headers: ${JSON.stringify(corsHeaders, null, 2)}\n`);
        resolve(true);
      } else {
        console.log(`❌ CORS falló con código: ${res.statusCode}`);
        console.log(`   Headers: ${JSON.stringify(corsHeaders, null, 2)}\n`);
        resolve(false);
      }
    });

    req.on('error', (err) => {
      console.log(`❌ Error verificando CORS: ${err.message}\n`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('❌ Timeout verificando CORS\n');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Ejecutar todas las pruebas
async function runTests() {
  console.log('🚀 Iniciando pruebas de conexión...\n');
  
  const results = {
    backend: await testBackendHealth(),
    agents: await testAgentsEndpoint(),
    chat: await testChatEndpoint(),
    cors: await testCORS()
  };

  console.log('📊 Resumen de resultados:');
  console.log(`   Backend Health: ${results.backend ? '✅' : '❌'}`);
  console.log(`   Agents Endpoint: ${results.agents ? '✅' : '❌'}`);
  console.log(`   Chat Endpoint: ${results.chat ? '✅' : '❌'}`);
  console.log(`   CORS: ${results.cors ? '✅' : '❌'}`);

  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 ¡Todas las pruebas pasaron! La conexión debería funcionar.');
  } else {
    console.log('\n⚠️  Algunas pruebas fallaron. Revisa los errores arriba.');
    console.log('\n💡 Sugerencias:');
    if (!results.backend) {
      console.log('   - Asegúrate de que el backend esté corriendo: cd backend && poetry run python main.py');
    }
    if (!results.cors) {
      console.log('   - Verifica la configuración CORS en backend/app/core/config.py');
    }
    if (!results.chat) {
      console.log('   - Revisa los logs del backend para errores en el endpoint de chat');
    }
  }
  
  console.log('\n🔧 Para iniciar el backend: cd backend && poetry run python main.py');
  console.log('🔧 Para iniciar el frontend: cd client && npm run dev');
}

// Ejecutar
runTests().catch(console.error);
