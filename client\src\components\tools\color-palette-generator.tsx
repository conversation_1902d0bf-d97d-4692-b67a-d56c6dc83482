"use client";

import { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import colorNamer from "color-namer";
import {
  Download,
  Image as ImageIcon,
  Co<PERSON>,
  Shuffle,
  Check,
  RotateCcw,
  Info,
  Save,
  Lock,
  Unlock,
  RefreshCw,
  Palette,
  Heart,
  Trash2,
  Edit,
  FolderOpen,
  Star,
} from "lucide-react";
import { usePalettes, validatePaletteData, normalizeHexColor } from "@/hooks/use-palettes";
import { useAuth } from "@/hooks/use-auth";
import type { CreateUserPaletteData, UserPalette } from "@/lib/supabase";

// Interface para cada color en la paleta
interface Color {
  hex: string;
  locked: boolean;
  name: string;
}

// Tipos de paletas disponibles
type PaletteType =
  | "analogous"
  | "monochromatic"
  | "triadic"
  | "complementary"
  | "random";

// Nombres descriptivos de los colores
const COLOR_NAMES = [
  "Rojo Coral",
  "Naranja Amanecer",
  "Amarillo Sol",
  "Verde Primavera",
  "Verde Aguacate",
  "Verde Bosque",
  "Turquesa Mar",
  "Azul Cielo",
  "Azul Profundo",
  "Índigo Noche",
  "Violeta Atardecer",
  "Púrpura Real",
  "Rosa Chicle",
  "Rosa Pastel",
  "Beige Arena",
  "Marrón Tierra",
  "Gris Nube",
  "Negro Carbón",
  "Blanco Nieve",
  "Crema Vainilla",
];

// Función para generar un color hexadecimal aleatorio
const generateRandomColor = (): string => {
  return `#${Math.floor(Math.random() * 16777215)
    .toString(16)
    .padStart(6, "0")}`;
};

// Función para convertir de HEX a HSL
const hexToHSL = (hex: string): [number, number, number] => {
  // Eliminar el # si existe
  hex = hex.replace(/^#/, "");

  // Convertir a RGB
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  // Encontrar el máximo y mínimo de RGB
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);

  // Calcular la luminosidad
  const l = (max + min) / 2;

  let h = 0;
  let s = 0;

  if (max !== min) {
    // Calcular la saturación
    s = l > 0.5 ? (max - min) / (2 - max - min) : (max - min) / (max + min);

    // Calcular el matiz
    if (max === r) {
      h = (g - b) / (max - min) + (g < b ? 6 : 0);
    } else if (max === g) {
      h = (b - r) / (max - min) + 2;
    } else {
      h = (r - g) / (max - min) + 4;
    }

    h /= 6;
  }

  return [h * 360, s * 100, l * 100];
};

// Función para convertir de HSL a HEX
const hslToHex = (h: number, s: number, l: number): string => {
  // Normalizar los valores
  h /= 360;
  s /= 100;
  l /= 100;

  let r: number, g: number, b: number;

  if (s === 0) {
    r = g = b = l;
  } else {
    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    r = hue2rgb(p, q, h + 1 / 3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1 / 3);
  }

  const toHex = (x: number): string => {
    const hex = Math.round(x * 255).toString(16);
    return hex.length === 1 ? "0" + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

// Mapa de traducción de nombres de colores del inglés al español
const translateColorName = (englishName: string): string => {
  const translations: { [key: string]: string } = {
    red: "Rojo",
    orange: "Naranja",
    yellow: "Amarillo",
    green: "Verde",
    blue: "Azul",
    purple: "Púrpura",
    pink: "Rosa",
    brown: "Marrón",
    grey: "Gris",
    gray: "Gris",
    black: "Negro",
    white: "Blanco",
    coral: "Coral",
    aqua: "Agua",
    azure: "Azur",
    beige: "Beige",
    crimson: "Carmesí",
    cyan: "Cian",
    fuchsia: "Fucsia",
    gold: "Dorado",
    indigo: "Índigo",
    ivory: "Marfil",
    lavender: "Lavanda",
    lime: "Lima",
    magenta: "Magenta",
    maroon: "Granate",
    navy: "Azul Marino",
    olive: "Oliva",
    salmon: "Salmón",
    silver: "Plateado",
    tan: "Canela",
    teal: "Verde Azulado",
    turquoise: "Turquesa",
    violet: "Violeta",
    sky: "Cielo",
    forest: "Bosque",
    sea: "Mar",
    midnight: "Medianoche",
    coffee: "Café",
    charcoal: "Carbón",
    chocolate: "Chocolate",
    steel: "Acero",
    slate: "Pizarra",
    honey: "Miel",
    ruby: "Rubí",
    mint: "Menta",
    plum: "Ciruela",
    sapphire: "Zafiro",
    amethyst: "Amatista",
    pear: "Pera",
    moss: "Musgo",
    emerald: "Esmeralda",
    topaz: "Topacio",
    peach: "Melocotón",
    sunset: "Atardecer",
    dawn: "Amanecer",
  };

  // Dividir el nombre en palabras
  const words = englishName.toLowerCase().split(" ");

  // Traducir cada palabra si existe en el diccionario
  const translatedWords = words.map((word) => {
    return translations[word] || word;
  });

  // Convertir la primera letra de cada palabra a mayúsculas
  const capitalizedWords = translatedWords.map(
    (word) => word.charAt(0).toUpperCase() + word.slice(1),
  );

  // Unir las palabras
  return capitalizedWords.join(" ");
};

// Obtener un nombre descriptivo para un color utilizando color-namer
const getColorName = (hex: string): string => {
  try {
    // Obtener nombres de colores utilizando color-namer
    const names = colorNamer(hex);

    // Priorizar diferentes paletas de nombres según el tipo de color
    const [h, s, l] = hexToHSL(hex);

    let name;

    // Usar diferentes paletas de nombres según el tipo de color
    if (s < 10) {
      // Para colores con baja saturación (grises)
      name = names.ntc[0].name;
    } else if (l < 20) {
      // Para colores muy oscuros
      name = names.ntc[0].name;
    } else if (l > 80) {
      // Para colores muy claros
      name = names.ntc[0].name;
    } else {
      // Para la mayoría de los colores, usar la paleta básica
      name = names.basic[0].name;

      // Si el color es más complejo, intentar con otras paletas
      if (name === "black" || name === "white" || name === "gray") {
        name = names.ntc[0].name;
      }
    }

    // Traducir el nombre al español
    return translateColorName(name);
  } catch (error) {
    console.error("Error al obtener el nombre del color:", error);

    // Si hay un error, usar la lógica anterior como fallback
    const [h, s, l] = hexToHSL(hex);

    // Lógica simplificada de fallback
    if (l < 20) return "Negro";
    if (l > 80) return "Blanco";
    if (s < 20) return "Gris";

    // Nombres básicos según el tono
    if ((h >= 0 && h <= 20) || h >= 340) return "Rojo";
    if (h >= 21 && h <= 45) return "Naranja";
    if (h >= 46 && h <= 70) return "Amarillo";
    if (h >= 71 && h <= 160) return "Verde";
    if (h >= 161 && h <= 200) return "Turquesa";
    if (h >= 201 && h <= 240) return "Azul";
    if (h >= 241 && h <= 280) return "Violeta";
    if (h >= 281 && h <= 320) return "Rosa";
    if (h >= 321 && h <= 339) return "Magenta";

    return "Color";
  }
};

// Función para generar paletas según el tipo
const generatePalette = (
  baseColor: string,
  paletteType: PaletteType,
  count: number = 5,
  lockedIndexes: number[] = [],
): Color[] => {
  const [h, s, l] = hexToHSL(baseColor);
  const palette: Color[] = new Array(count); // Inicializar array con el tamaño correcto

  switch (paletteType) {
    case "monochromatic":
      // Mantenemos el mismo tono, variamos saturación y luminosidad
      for (let i = 0; i < count; i++) {
        if (lockedIndexes.includes(i)) {
          // Saltamos los colores bloqueados, se llenarán después
          continue;
        }

        const newL = Math.max(20, Math.min(90, l - 30 + i * 15));
        const newS = Math.max(30, Math.min(95, s - 20 + i * 10));
        const hex = hslToHex(h, newS, newL);
        palette[i] = { hex, locked: false, name: getColorName(hex) };
      }
      break;

    case "analogous":
      // Colores con tonos cercanos en el círculo cromático
      for (let i = 0; i < count; i++) {
        if (lockedIndexes.includes(i)) {
          continue;
        }

        const newH = (h + (i - Math.floor(count / 2)) * 30) % 360;
        const newS = Math.max(30, Math.min(95, s - 5 + i * 3));
        const newL = Math.max(30, Math.min(85, l - 10 + i * 5));
        const hex = hslToHex(newH, newS, newL);
        palette[i] = { hex, locked: false, name: getColorName(hex) };
      }
      break;

    case "complementary":
      // Color original y su complementario (opuesto en el círculo cromático)
      for (let i = 0; i < count; i++) {
        if (lockedIndexes.includes(i)) {
          continue;
        }

        // 🔧 FIX: Evitar división por cero cuando count = 1
        const hueStep = count > 1 ? 180 / (count - 1) : 0;
        const newH = (h + i * hueStep) % 360;
        const newS = Math.max(30, Math.min(95, s - 5 + i * 3));
        const newL = Math.max(30, Math.min(85, l - 10 + i * 5));
        const hex = hslToHex(newH, newS, newL);
        palette[i] = { hex, locked: false, name: getColorName(hex) };
      }
      break;

    case "triadic":
      // Tres colores equidistantes en el círculo cromático
      for (let i = 0; i < count; i++) {
        if (lockedIndexes.includes(i)) {
          continue;
        }

        const newH = (h + i * (360 / count)) % 360;
        const newS = Math.max(30, Math.min(95, s));
        const newL = Math.max(30, Math.min(85, l - 5 + i * 3));
        const hex = hslToHex(newH, newS, newL);
        palette[i] = { hex, locked: false, name: getColorName(hex) };
      }
      break;

    case "random":
      // Colores aleatorios - AQUÍ SÍ TIENE SENTIDO INCLUSO CON COLORES BLOQUEADOS
      for (let i = 0; i < count; i++) {
        if (lockedIndexes.includes(i)) {
          continue;
        }

        // 🔧 FIX: Generar colores aleatorios con mejor distribución
        // Usar diferentes semillas para mayor variedad
        const randomHue = Math.floor(Math.random() * 360);
        const randomSat = Math.floor(Math.random() * 70) + 30; // 30-100%
        const randomLight = Math.floor(Math.random() * 60) + 20; // 20-80%

        const hex = hslToHex(randomHue, randomSat, randomLight);
        palette[i] = { hex, locked: false, name: getColorName(hex) };
      }
      break;
  }

  return palette;
};

// Componente de tarjeta de color individual
const ColorCard = ({
  color,
  onToggleLock,
  onCopy,
  index,
}: {
  color: Color;
  onToggleLock: () => void;
  onCopy: () => void;
  index: number;
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(color.hex);
    setCopied(true);
    onCopy();

    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const textColor = hexToHSL(color.hex)[2] > 60 ? "#000000" : "#ffffff";

  return (
    <motion.div
      className={`relative rounded-xl overflow-hidden bg-white shadow-sm border ${color.locked ? "border-gray-400" : "border-gray-200"} hover:shadow-md`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.08 }}
    >
      <div
        className="h-32 flex items-center justify-center relative"
        style={{
          backgroundColor: color.hex,
          boxShadow: color.locked ? "inset 0 0 0 2px rgba(0,0,0,0.1)" : "none",
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-black/5"></div>
        <div className="absolute top-3 right-3 z-10">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  style={{
                    backgroundColor: color.locked
                      ? hexToHSL(color.hex)[2] > 60
                        ? "rgba(0,0,0,0.35)"
                        : "rgba(255,255,255,0.35)"
                      : hexToHSL(color.hex)[2] > 60
                        ? "rgba(0,0,0,0.2)"
                        : "rgba(255,255,255,0.2)",
                    borderColor: color.locked
                      ? hexToHSL(color.hex)[2] > 60
                        ? "rgba(0,0,0,0.5)"
                        : "rgba(255,255,255,0.5)"
                      : hexToHSL(color.hex)[2] > 60
                        ? "rgba(0,0,0,0.3)"
                        : "rgba(255,255,255,0.3)",
                  }}
                  className={`w-8 h-8 hover:bg-black/30 hover:border-black/30 shadow-sm ${color.locked ? "ring-2 ring-inset ring-black/20 dark:ring-white/30" : ""}`}
                  onClick={onToggleLock}
                >
                  {color.locked ? (
                    <Lock
                      className="h-4 w-4"
                      style={{
                        color: hexToHSL(color.hex)[2] > 60 ? "#000" : "#fff",
                      }}
                    />
                  ) : (
                    <Unlock
                      className="h-4 w-4"
                      style={{
                        color: hexToHSL(color.hex)[2] > 60 ? "#000" : "#fff",
                      }}
                    />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{color.locked ? "Desbloquear color" : "Bloquear color"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div>
            <div className="flex items-center gap-1.5">
              <h3 className="text-sm font-medium text-gray-700">
                {color.hex.toUpperCase()}
              </h3>
              {color.locked && (
                <span className="inline-flex items-center justify-center bg-gray-200 text-gray-700 w-5 h-5 rounded-full">
                  <Lock className="h-3 w-3" />
                </span>
              )}
            </div>
            <p className="text-xl font-mono font-bold">
              {color.hex.toUpperCase()}
            </p>
          </div>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-9 w-9"
                  onClick={handleCopy}
                >
                  {copied ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Copiar código hexadecimal</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    </motion.div>
  );
};

// Componente principal del generador de paletas
export default function ColorPaletteGenerator() {
  const [palettes, setPalettes] = useState<{
    current: Color[];
    saved: Color[][];
  }>({
    current: [],
    saved: [],
  });

  const [settings, setSettings] = useState({
    paletteType: "analogous" as PaletteType,
    colorCount: 5,
  });

  const [copiedMsg, setCopiedMsg] = useState<string | null>(null);

  // Database integration state
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showLoadDialog, setShowLoadDialog] = useState(false);
  const [paletteName, setPaletteName] = useState("");
  const [paletteDescription, setPaletteDescription] = useState("");
  const [selectedPalette, setSelectedPalette] = useState<UserPalette | null>(null);
  const [activeTab, setActiveTab] = useState<"generated" | "favorites">("generated");

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Hooks for authentication and database operations
  const { user } = useAuth();

  // Hook for recent palettes (last 10)
  const {
    palettes: recentPalettes,
    isLoadingPalettes: isLoadingRecent,
    createPalette,
    updatePalette,
    deletePalette,
    toggleFavorite,
    refetchPalettes: refetchRecent
  } = usePalettes({ limit: 10 });

  // Hook for favorite palettes (unlimited)
  const {
    palettes: favoritePalettes,
    isLoadingPalettes: isLoadingFavorites,
    refetchPalettes: refetchFavorites
  } = usePalettes({ is_favorite: true, limit: 100 });

  // Combined loading state
  const isLoadingPalettes = isLoadingRecent || isLoadingFavorites;

  // Get current palettes based on active tab
  const currentPalettes = activeTab === "generated" ? recentPalettes : favoritePalettes;
  const currentLoading = activeTab === "generated" ? isLoadingRecent : isLoadingFavorites;

  // Inicializar paleta al cargar
  useEffect(() => {
    const initialColor = generateRandomColor();
    const initialPalette = generatePalette(
      initialColor,
      settings.paletteType,
      settings.colorCount,
    );
    setPalettes((prev) => ({
      ...prev,
      current: initialPalette,
    }));
  }, [settings.paletteType, settings.colorCount]);

  // Función para extraer los colores dominantes de una imagen
  const extractDominantColors = (
    imageData: Uint8ClampedArray,
    width: number,
    height: number,
    colorCount: number,
  ): string[] => {
    // Crear un mapa para contar la frecuencia de cada color
    const colorMap: { [key: string]: number } = {};

    // Recopilar muestras de píxeles (1 de cada 10 para mejorar rendimiento)
    const sampleRate = 10;

    for (let y = 0; y < height; y += sampleRate) {
      for (let x = 0; x < width; x += sampleRate) {
        const idx = (y * width + x) * 4;

        // Obtener valores RGB
        let r = imageData[idx];
        let g = imageData[idx + 1];
        let b = imageData[idx + 2];
        const a = imageData[idx + 3]; // Alpha channel

        // Skip transparent pixels
        if (a < 128) continue;

        // Reducir la precisión del color para agrupar colores similares
        // Esto reduce cada canal a 16 valores posibles en lugar de 256
        r = Math.round(r / 16) * 16;
        g = Math.round(g / 16) * 16;
        b = Math.round(b / 16) * 16;

        // 🔧 FIX: Convertir a hexadecimal de forma segura
        // Usar >>> 0 para convertir a unsigned 32-bit integer y evitar números negativos
        const rgbValue = ((r << 16) | (g << 8) | b) >>> 0;
        const hex = `#${rgbValue.toString(16).padStart(6, "0")}`;

        // Incrementar el contador para este color
        colorMap[hex] = (colorMap[hex] || 0) + 1;
      }
    }

    // Ordenar colores por frecuencia
    const sortedColors = Object.entries(colorMap)
      .sort((a, b) => b[1] - a[1])
      .map((entry) => entry[0]);

    // Filtrar colores muy similares
    const uniqueColors: string[] = [];

    // Función para determinar si un color es significativamente diferente a los ya seleccionados
    const isDistinct = (color: string, selectedColors: string[]): boolean => {
      // Convertir a RGB
      const r1 = parseInt(color.substring(1, 3), 16);
      const g1 = parseInt(color.substring(3, 5), 16);
      const b1 = parseInt(color.substring(5, 7), 16);

      for (const selected of selectedColors) {
        const r2 = parseInt(selected.substring(1, 3), 16);
        const g2 = parseInt(selected.substring(3, 5), 16);
        const b2 = parseInt(selected.substring(5, 7), 16);

        // Calcular distancia euclidiana entre colores
        const distance = Math.sqrt(
          Math.pow(r1 - r2, 2) + Math.pow(g1 - g2, 2) + Math.pow(b1 - b2, 2),
        );

        // Si la distancia es pequeña, consideramos que es un color similar
        if (distance < 40) {
          return false;
        }
      }

      return true;
    };

    // Recolectar colores únicos
    for (const color of sortedColors) {
      if (uniqueColors.length < colorCount) {
        if (uniqueColors.length === 0 || isDistinct(color, uniqueColors)) {
          uniqueColors.push(color);
        }
      } else {
        break;
      }
    }

    // Si no tenemos suficientes colores, agregar colores adicionales
    while (uniqueColors.length < colorCount) {
      // Generar un color aleatorio complementario a los existentes
      const baseColor = uniqueColors[0] || "#ff0000";
      const [h, s, l] = hexToHSL(baseColor);

      // Usar un desvío basado en el índice para obtener colores complementarios
      const hueOffset = (uniqueColors.length * 137) % 360;
      const newHue = (h + hueOffset) % 360;

      // Generar el nuevo color
      const newColor = hslToHex(newHue, s, l);

      uniqueColors.push(newColor);
    }

    return uniqueColors;
  };

  // Manejar carga de imagen para extraer colores
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      console.log("❌ No file selected");
      return;
    }

    console.log("🖼️ Processing image:", file.name, file.type, file.size);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setCopiedMsg("Por favor selecciona un archivo de imagen válido");
      setTimeout(() => setCopiedMsg(null), 2000);
      return;
    }

    // Mostrar mensaje de carga
    setCopiedMsg("Extrayendo colores de la imagen...");

    const img = document.createElement("img");
    img.onload = () => {
      console.log("🖼️ Image loaded:", img.width, "x", img.height);

      const canvas = canvasRef.current;
      if (!canvas) {
        console.error("❌ Canvas not available");
        setCopiedMsg("Error: Canvas no disponible");
        setTimeout(() => setCopiedMsg(null), 2000);
        return;
      }

      const ctx = canvas.getContext("2d");
      if (!ctx) {
        console.error("❌ Canvas context not available");
        setCopiedMsg("Error: Contexto de canvas no disponible");
        setTimeout(() => setCopiedMsg(null), 2000);
        return;
      }

      try {
        // Redimensionar el canvas al tamaño de la imagen
        canvas.width = img.width;
        canvas.height = img.height;

        // Dibujar la imagen en el canvas
        ctx.drawImage(img, 0, 0);

        // Extraer los datos de la imagen
        const imageData = ctx.getImageData(
          0,
          0,
          canvas.width,
          canvas.height,
        ).data;

        console.log("🎨 Extracting colors from image data...");

        // Extraer los colores dominantes
        const dominantHexColors = extractDominantColors(
          imageData,
          canvas.width,
          canvas.height,
          settings.colorCount,
        );

        console.log("🎨 Extracted colors:", dominantHexColors);

        // 🔧 FIX: Validate extracted colors
        const validColors = dominantHexColors.filter(hex => {
          const isValid = /^#[0-9A-F]{6}$/i.test(hex);
          if (!isValid) {
            console.warn("⚠️ Invalid hex color:", hex);
          }
          return isValid;
        });

        if (validColors.length === 0) {
          console.error("❌ No valid colors extracted");
          setCopiedMsg("Error: No se pudieron extraer colores válidos de la imagen");
          setTimeout(() => setCopiedMsg(null), 2000);
          return;
        }

        // Convertir a objetos Color
        const colors: Color[] = validColors.map((hex) => ({
          hex,
          locked: false,
          name: getColorName(hex),
        }));

        console.log("🎨 Final color objects:", colors);

        // Actualizar la paleta
        setPalettes((prev) => ({
          ...prev,
          current: colors,
        }));

        // Limpiar mensaje de carga
        setCopiedMsg(`${colors.length} colores extraídos con éxito`);
        setTimeout(() => setCopiedMsg(null), 2000);
      } catch (error) {
        console.error("❌ Error processing image:", error);
        setCopiedMsg("Error al procesar la imagen");
        setTimeout(() => setCopiedMsg(null), 2000);
      }
    };

    img.onerror = (error) => {
      console.error("❌ Error loading image:", error);
      setCopiedMsg("Error al cargar la imagen");
      setTimeout(() => setCopiedMsg(null), 2000);
    };

    img.src = URL.createObjectURL(file);
  };

  // Regenerar la paleta basada en el tipo seleccionado
  const regeneratePalette = useCallback(() => {
    if (palettes.current.length === 0) return;

    const lockedColors = palettes.current
      .map((color, index) => ({ color, index }))
      .filter((item) => item.color.locked);

    // 🔧 FIX: Generar SIEMPRE un nuevo color base para regeneración ilimitada
    let baseColor: string;

    if (lockedColors.length > 0) {
      // 🚀 FIX CRÍTICO: Incluso con colores bloqueados, agregar variación
      if (settings.paletteType === "random") {
        // Para random, siempre generar nuevo color base
        baseColor = generateRandomColor();
      } else {
        // Para otros tipos, usar color bloqueado pero con variación aleatoria
        const lockedColor = lockedColors[Math.floor(Math.random() * lockedColors.length)].color.hex;
        const [h, s, l] = hexToHSL(lockedColor);

        // Agregar variación aleatoria al color base para más diversidad
        const hueVariation = (Math.random() - 0.5) * 60; // ±30 grados
        const satVariation = (Math.random() - 0.5) * 20; // ±10%
        const lightVariation = (Math.random() - 0.5) * 20; // ±10%

        const newH = (h + hueVariation + 360) % 360;
        const newS = Math.max(20, Math.min(100, s + satVariation));
        const newL = Math.max(20, Math.min(80, l + lightVariation));

        baseColor = hslToHex(newH, newS, newL);
      }
    } else {
      // 🚀 FIX CRÍTICO: Generar NUEVO color aleatorio cada vez
      // Esto permite regeneración infinita sin colores bloqueados
      baseColor = generateRandomColor();
    }

    const lockedIndexes = lockedColors.map((item) => item.index);

    const newPalette = generatePalette(
      baseColor,
      settings.paletteType,
      settings.colorCount,
      lockedIndexes,
    );

    // Preservar los colores bloqueados en sus posiciones correctas
    lockedColors.forEach(({ color, index }) => {
      newPalette[index] = { ...color };
    });

    // Verificar que no hay espacios vacíos (undefined) en la paleta
    for (let i = 0; i < settings.colorCount; i++) {
      if (!newPalette[i]) {
        // Si hay un espacio vacío, generar un color según el tipo de paleta
        const [h, s, l] = hexToHSL(baseColor);
        let hex: string;

        switch (settings.paletteType) {
          case "random":
            // 🔧 FIX: Usar la misma lógica mejorada de random
            const randomHue = Math.floor(Math.random() * 360);
            const randomSat = Math.floor(Math.random() * 70) + 30;
            const randomLight = Math.floor(Math.random() * 60) + 20;
            hex = hslToHex(randomHue, randomSat, randomLight);
            break;
          case "monochromatic":
            const newL = Math.max(20, Math.min(90, l - 30 + i * 15));
            const newS = Math.max(30, Math.min(95, s - 20 + i * 10));
            hex = hslToHex(h, newS, newL);
            break;
          case "analogous":
            const newH = (h + (i - Math.floor(settings.colorCount / 2)) * 30) % 360;
            const newSAnalog = Math.max(30, Math.min(95, s - 5 + i * 3));
            const newLAnalog = Math.max(30, Math.min(85, l - 10 + i * 5));
            hex = hslToHex(newH, newSAnalog, newLAnalog);
            break;
          case "complementary":
            // 🔧 FIX: Usar la misma lógica mejorada de complementary
            const hueStep = settings.colorCount > 1 ? 180 / (settings.colorCount - 1) : 0;
            const newHComp = (h + i * hueStep) % 360;
            const newSComp = Math.max(30, Math.min(95, s - 5 + i * 3));
            const newLComp = Math.max(30, Math.min(85, l - 10 + i * 5));
            hex = hslToHex(newHComp, newSComp, newLComp);
            break;
          case "triadic":
            const newHTriad = (h + i * (360 / settings.colorCount)) % 360;
            const newSTriad = Math.max(30, Math.min(95, s));
            const newLTriad = Math.max(30, Math.min(85, l - 5 + i * 3));
            hex = hslToHex(newHTriad, newSTriad, newLTriad);
            break;
          default:
            // 🔧 FIX: Default también usa lógica mejorada
            const defHue = Math.floor(Math.random() * 360);
            const defSat = Math.floor(Math.random() * 70) + 30;
            const defLight = Math.floor(Math.random() * 60) + 20;
            hex = hslToHex(defHue, defSat, defLight);
        }

        newPalette[i] = {
          hex,
          locked: false,
          name: getColorName(hex)
        };
      }
    }

    setPalettes((prev) => ({
      ...prev,
      current: newPalette,
    }));
  }, [palettes.current, settings]);

  // Guardar la paleta actual
  const savePalette = () => {
    setPalettes((prev) => ({
      ...prev,
      saved: [...prev.saved, [...prev.current]],
    }));

    setCopiedMsg("Paleta guardada");
    setTimeout(() => setCopiedMsg(null), 2000);
  };

  // Exportar la paleta en formato CSS
  const exportCSS = () => {
    const cssVars = palettes.current
      .map((color, index) => `  --color-${index + 1}: ${color.hex};`)
      .join("\n");

    const css = `:root {\n${cssVars}\n}`;

    navigator.clipboard.writeText(css);
    setCopiedMsg("CSS copiado al portapapeles");
    setTimeout(() => setCopiedMsg(null), 2000);
  };

  // Exportar la paleta en formato JSON
  const exportJSON = () => {
    const json = JSON.stringify(
      palettes.current.map((color) => ({ hex: color.hex, name: color.name })),
      null,
      2,
    );

    navigator.clipboard.writeText(json);
    setCopiedMsg("JSON copiado al portapapeles");
    setTimeout(() => setCopiedMsg(null), 2000);
  };

  // Alternar bloqueo de un color
  const toggleColorLock = (index: number) => {
    setPalettes((prev) => {
      const newCurrent = [...prev.current];
      newCurrent[index] = {
        ...newCurrent[index],
        locked: !newCurrent[index].locked,
      };

      return {
        ...prev,
        current: newCurrent,
      };
    });
  };

  // Actualizar los ajustes del generador
  const updateSettings = (key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Manejar la notificación de copiado
  const handleCopied = () => {
    setCopiedMsg("Color copiado al portapapeles");
    setTimeout(() => setCopiedMsg(null), 2000);
  };



  // Database integration functions
  const handleSavePaletteToDatabase = async () => {
    console.log("🎨 Starting palette save process...");

    if (!user) {
      console.error("❌ No user authenticated");
      setCopiedMsg("Debes iniciar sesión para guardar paletas");
      setTimeout(() => setCopiedMsg(null), 2000);
      return;
    }

    if (!paletteName.trim()) {
      console.error("❌ Palette name is empty");
      setCopiedMsg("El nombre de la paleta es requerido");
      setTimeout(() => setCopiedMsg(null), 2000);
      return;
    }

    if (palettes.current.length === 0) {
      console.error("❌ No colors in current palette");
      setCopiedMsg("La paleta debe tener al menos un color");
      setTimeout(() => setCopiedMsg(null), 2000);
      return;
    }

    // 🔧 FIX: Ensure all colors are valid hex format
    const colors = palettes.current.map(color => {
      const normalizedHex = normalizeHexColor(color.hex);
      console.log(`🎨 Color: ${color.hex} → ${normalizedHex}`);
      return normalizedHex;
    });

    const paletteData: CreateUserPaletteData = {
      name: paletteName.trim(),
      colors,
      description: paletteDescription.trim() || undefined,
      tags: [],
      is_favorite: false
    };

    console.log("🎨 Palette data to save:", paletteData);

    // Validate data
    const errors = validatePaletteData(paletteData);
    if (errors.length > 0) {
      console.error("❌ Validation errors:", errors);
      setCopiedMsg(errors[0]);
      setTimeout(() => setCopiedMsg(null), 3000);
      return;
    }

    try {
      console.log("🎨 Calling createPalette...");
      const result = await createPalette(paletteData);
      console.log("✅ Palette saved successfully:", result);

      setShowSaveDialog(false);
      setPaletteName("");
      setPaletteDescription("");
      setCopiedMsg("Paleta guardada exitosamente");
      setTimeout(() => setCopiedMsg(null), 2000);

      // Refresh both palettes lists
      refetchRecent();
      refetchFavorites();
    } catch (error) {
      console.error("❌ Error saving palette:", error);
      setCopiedMsg(`Error al guardar: ${error instanceof Error ? error.message : 'Error desconocido'}`);
      setTimeout(() => setCopiedMsg(null), 3000);
    }
  };

  const handleLoadPalette = (palette: UserPalette) => {
    const colors: Color[] = palette.colors.map(hex => ({
      hex: normalizeHexColor(hex),
      locked: false,
      name: getColorName(hex)
    }));

    setPalettes(prev => ({
      ...prev,
      current: colors
    }));

    setShowLoadDialog(false);
    setCopiedMsg(`Paleta "${palette.name}" cargada`);
    setTimeout(() => setCopiedMsg(null), 2000);
  };

  const handleDeletePalette = async (paletteId: string) => {
    try {
      await deletePalette(paletteId);
      // Refresh both lists since a palette could be in either
      refetchRecent();
      refetchFavorites();
    } catch (error) {
      console.error("Error deleting palette:", error);
    }
  };

  const handleToggleFavorite = async (paletteId: string, isFavorite: boolean) => {
    try {
      await toggleFavorite({ id: paletteId, is_favorite: !isFavorite });
      // Refresh both lists since favorite status affects both tabs
      refetchRecent();
      refetchFavorites();
    } catch (error) {
      console.error("Error toggling favorite:", error);
    }
  };

  // 🚀 Atajo de teclado: Espacio para regenerar paleta
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Solo si no estamos escribiendo en un input
      if (e.code === 'Space' && e.target === document.body) {
        e.preventDefault();
        regeneratePalette();

        // Mostrar feedback visual
        setCopiedMsg("¡Nueva paleta generada! 🎨");
        setTimeout(() => setCopiedMsg(null), 1500);
      }
    };

    // Agregar el event listener
    document.addEventListener('keydown', handleKeyPress);

    // Cleanup al desmontar el componente
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [regeneratePalette]);

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          Generador de Paletas de Color
        </h1>
        <p className="text-gray-600">
          Crea paletas de colores armónicas para tus diseños y proyectos de
          marketing.
        </p>
      </div>

      {/* Canvas oculto para procesar imágenes */}
      <canvas ref={canvasRef} className="hidden" />
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        onChange={handleImageUpload}
      />

      {/* Controles principales */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Configuración</CardTitle>
            <CardDescription>
              Personaliza cómo se generarán tus paletas de colores
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className="mb-2 block">Tipo de paleta</Label>
                <Tabs
                  defaultValue="analogous"
                  className="w-full"
                  value={settings.paletteType}
                  onValueChange={(val) => updateSettings("paletteType", val)}
                >
                  <TabsList className="grid grid-cols-3 mb-2 p-1">
                    <TabsTrigger
                      value="analogous"
                      className="py-2 font-medium text-sm"
                    >
                      <span className="px-1">Análoga</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="monochromatic"
                      className="py-2 font-medium text-sm"
                    >
                      <span className="px-1">Monocromática</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="complementary"
                      className="py-2 font-medium text-sm"
                    >
                      <span className="px-1">Complementaria</span>
                    </TabsTrigger>
                  </TabsList>
                  <TabsList className="grid grid-cols-2 p-1">
                    <TabsTrigger
                      value="triadic"
                      className="py-2 font-medium text-sm"
                    >
                      <span className="px-1">Triádica</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="random"
                      className="py-2 font-medium text-sm"
                    >
                      <span className="px-1">Aleatoria</span>
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              <div>
                <div className="mb-6">
                  <Label className="mb-2 block">Número de colores</Label>
                  <div className="flex items-center gap-4">
                    <Slider
                      defaultValue={[5]}
                      min={3}
                      max={10}
                      step={1}
                      value={[settings.colorCount]}
                      onValueChange={([value]) =>
                        updateSettings("colorCount", value)
                      }
                      className="flex-1"
                    />
                    <span className="text-gray-700 font-medium w-6 text-center">
                      {settings.colorCount}
                    </span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 mt-2">
                  <Button
                    variant="default"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex-1"
                  >
                    <ImageIcon className="w-4 h-4 mr-2" />
                    Extraer de imagen
                  </Button>

                  <Button
                    variant="outline"
                    onClick={regeneratePalette}
                    className="flex-1"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Regenerar
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Gestionar Paletas</CardTitle>
            <CardDescription>Guarda, carga y comparte tus paletas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {user ? (
                <>
                  <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
                    <DialogTrigger asChild>
                      <Button
                        variant="default"
                        className="w-full justify-start"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        Guardar en la nube
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Guardar Paleta</DialogTitle>
                        <DialogDescription>
                          Guarda tu paleta actual en la nube para acceder a ella desde cualquier dispositivo.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="palette-name">Nombre de la paleta</Label>
                          <Input
                            id="palette-name"
                            value={paletteName}
                            onChange={(e) => setPaletteName(e.target.value)}
                            placeholder="Mi paleta increíble"
                            maxLength={100}
                          />
                        </div>
                        <div>
                          <Label htmlFor="palette-description">Descripción (opcional)</Label>
                          <Input
                            id="palette-description"
                            value={paletteDescription}
                            onChange={(e) => setPaletteDescription(e.target.value)}
                            placeholder="Paleta para mi proyecto de branding..."
                            maxLength={500}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                          Cancelar
                        </Button>
                        <Button onClick={handleSavePaletteToDatabase}>
                          Guardar
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Dialog open={showLoadDialog} onOpenChange={setShowLoadDialog}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                      >
                        <FolderOpen className="w-4 h-4 mr-2" />
                        Cargar paleta guardada
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Paletas Guardadas</DialogTitle>
                        <DialogDescription>
                          Selecciona una paleta para cargarla en el generador.
                        </DialogDescription>
                      </DialogHeader>

                      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "generated" | "favorites")} className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                          <TabsTrigger value="generated" className="flex items-center gap-2">
                            <Palette className="h-4 w-4" />
                            Generadas ({recentPalettes.length})
                          </TabsTrigger>
                          <TabsTrigger value="favorites" className="flex items-center gap-2">
                            <Heart className="h-4 w-4" />
                            Favoritas ({favoritePalettes.length})
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="generated" className="space-y-4 mt-4">
                          {currentLoading ? (
                            <div className="text-center py-8">
                              <p>Cargando paletas...</p>
                            </div>
                          ) : recentPalettes.length === 0 ? (
                            <div className="text-center py-8">
                              <p className="text-gray-500">No tienes paletas generadas aún.</p>
                              <p className="text-sm text-gray-400 mt-2">Las últimas 10 paletas aparecerán aquí.</p>
                            </div>
                          ) : (
                            <div className="grid gap-4 max-h-96 overflow-y-auto">
                              {recentPalettes.map((palette) => (
                              <Card key={palette.id} className="overflow-hidden">
                                <div className="flex">
                                  <div className="flex flex-1">
                                    {palette.colors.map((color, index) => (
                                      <div
                                        key={index}
                                        className="h-16 flex-1 min-w-[40px]"
                                        style={{ backgroundColor: color }}
                                      />
                                    ))}
                                  </div>
                                </div>
                                <CardContent className="p-4">
                                  <div className="flex items-center justify-between">
                                    <div className="flex-1">
                                      <div className="flex items-center gap-2">
                                        <h3 className="font-medium">{palette.name}</h3>
                                        {palette.is_favorite && (
                                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                        )}
                                      </div>
                                      {palette.description && (
                                        <p className="text-sm text-gray-500 mt-1">{palette.description}</p>
                                      )}
                                      <p className="text-xs text-gray-400 mt-1">
                                        {palette.colors.length} colores • {new Date(palette.created_at).toLocaleDateString()}
                                      </p>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleToggleFavorite(palette.id, palette.is_favorite)}
                                      >
                                        <Heart className={`h-4 w-4 ${palette.is_favorite ? 'text-red-500 fill-current' : ''}`} />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleDeletePalette(palette.id)}
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="default"
                                        size="sm"
                                        onClick={() => handleLoadPalette(palette)}
                                      >
                                        Cargar
                                      </Button>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                              ))}
                            </div>
                          )}
                        </TabsContent>

                        <TabsContent value="favorites" className="space-y-4 mt-4">
                          {isLoadingFavorites ? (
                            <div className="text-center py-8">
                              <p>Cargando favoritas...</p>
                            </div>
                          ) : favoritePalettes.length === 0 ? (
                            <div className="text-center py-8">
                              <p className="text-gray-500">No tienes paletas favoritas aún.</p>
                              <p className="text-sm text-gray-400 mt-2">Marca paletas como favoritas para verlas aquí.</p>
                            </div>
                          ) : (
                            <div className="grid gap-4 max-h-96 overflow-y-auto">
                              {favoritePalettes.map((palette) => (
                                <Card key={palette.id} className="overflow-hidden">
                                  <div className="flex">
                                    <div className="flex flex-1">
                                      {palette.colors.map((color, index) => (
                                        <div
                                          key={index}
                                          className="h-16 flex-1 min-w-[40px]"
                                          style={{ backgroundColor: color }}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                  <CardContent className="p-4">
                                    <div className="flex items-center justify-between">
                                      <div className="flex-1">
                                        <div className="flex items-center gap-2">
                                          <h3 className="font-medium">{palette.name}</h3>
                                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                        </div>
                                        {palette.description && (
                                          <p className="text-sm text-gray-500 mt-1">{palette.description}</p>
                                        )}
                                        <p className="text-xs text-gray-400 mt-1">
                                          {palette.colors.length} colores • {new Date(palette.created_at).toLocaleDateString()}
                                        </p>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          onClick={() => handleToggleFavorite(palette.id, palette.is_favorite)}
                                        >
                                          <Heart className="h-4 w-4 text-red-500 fill-current" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          onClick={() => handleDeletePalette(palette.id)}
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                        <Button
                                          variant="default"
                                          size="sm"
                                          onClick={() => handleLoadPalette(palette)}
                                        >
                                          Cargar
                                        </Button>
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                            </div>
                          )}
                        </TabsContent>
                      </Tabs>
                    </DialogContent>
                  </Dialog>
                </>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-500 mb-3">
                    Inicia sesión para guardar tus paletas en la nube
                  </p>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={savePalette}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Guardar localmente
                  </Button>
                </div>
              )}

              <div className="border-t pt-3 space-y-2">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={exportCSS}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copiar como variables CSS
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={exportJSON}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copiar como JSON
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Paleta actual - Versión Premium */}
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-bold text-gray-800">Tu paleta actual</h2>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <span>Presiona</span>
              <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Espacio</kbd>
              <span>para regenerar</span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={regeneratePalette}>
              <Shuffle className="w-4 h-4 mr-1" />
              Generar nueva
            </Button>


          </div>
        </div>

        {/* Visualización Premium de la Paleta */}
        <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-200 bg-white">
          <motion.div
            className="flex h-96"
            layout
          >
            {palettes.current.map((color, index) => {
              const textColor = hexToHSL(color.hex)[2] > 60 ? "#000000" : "#ffffff";
              const isLight = textColor === "#000000";

              return (
                <motion.div
                  key={`${color.hex}-${index}`}
                  className="flex-1 relative group cursor-pointer"
                  style={{ backgroundColor: color.hex }}
                  onClick={() => {
                    navigator.clipboard.writeText(color.hex);
                    handleCopied();
                  }}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  whileHover={{ scale: 1.02, zIndex: 10 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {/* Lock Button */}
                  <div className="absolute top-4 right-4 z-10">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleColorLock(index);
                      }}
                      className={`
                        w-10 h-10 rounded-full transition-all duration-200
                        ${color.locked
                          ? isLight
                            ? 'bg-black/20 hover:bg-black/30 text-black'
                            : 'bg-white/20 hover:bg-white/30 text-white'
                          : 'opacity-0 group-hover:opacity-100 hover:bg-black/10'
                        }
                      `}
                      style={{ color: textColor }}
                    >
                      {color.locked ? (
                        <Lock className="h-4 w-4" />
                      ) : (
                        <Unlock className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  {/* Color Information */}
                  <div className="absolute inset-0 flex flex-col justify-center items-center text-center p-6">
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1 + 0.2 }}
                    >
                      <h3
                        className="text-3xl font-bold mb-2 tracking-wider"
                        style={{ color: textColor }}
                      >
                        {color.hex.toUpperCase()}
                      </h3>
                      <p
                        className="text-lg opacity-80 font-medium"
                        style={{ color: textColor }}
                      >
                        {color.name}
                      </p>
                    </motion.div>
                  </div>

                  {/* Locked Indicator */}
                  {color.locked && (
                    <div className="absolute bottom-4 left-4">
                      <div
                        className="flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium"
                        style={{
                          backgroundColor: isLight ? 'rgba(0,0,0,0.1)' : 'rgba(255,255,255,0.1)',
                          color: textColor
                        }}
                      >
                        <Lock className="h-3 w-3" />
                        Bloqueado
                      </div>
                    </div>
                  )}

                  {/* Hover Hint */}
                  <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <div
                      className="text-sm font-medium px-2 py-1 rounded"
                      style={{
                        backgroundColor: isLight ? 'rgba(0,0,0,0.1)' : 'rgba(255,255,255,0.1)',
                        color: textColor
                      }}
                    >
                      Click para copiar
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>

          {/* Bottom Info Bar */}
          <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div>
                Haz clic en cualquier color para copiarlo • Usa el candado para bloquear colores
              </div>
              <div className="flex items-center gap-4">
                <span>Tipo: {settings.paletteType}</span>
                <span>•</span>
                <span>{palettes.current.filter(c => c.locked).length} bloqueados</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Paletas guardadas */}
      {palettes.saved.length > 0 && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-800">
              Paletas guardadas
            </h2>
            <span className="text-sm text-gray-500">
              {palettes.saved.length}{" "}
              {palettes.saved.length === 1 ? "paleta" : "paletas"}
            </span>
          </div>

          <div className="grid gap-6">
            {palettes.saved.map((palette, paletteIndex) => (
              <Card key={paletteIndex} className="overflow-hidden">
                <div className="flex flex-wrap">
                  {palette.map((color, colorIndex) => (
                    <div
                      key={colorIndex}
                      className="h-12 flex-1 min-w-[60px] relative group"
                      style={{ backgroundColor: color.hex }}
                    >
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 bg-black/30 flex items-center justify-center transition-opacity duration-200">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-white"
                                onClick={() =>
                                  navigator.clipboard.writeText(color.hex)
                                }
                              >
                                <Copy className="h-3.5 w-3.5" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Copiar {color.hex}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  ))}
                </div>
                <CardContent className="flex justify-between items-center p-3">
                  <p className="text-sm text-gray-700">
                    Paleta #{paletteIndex + 1} (
                    {palette.map((c) => c.hex.toUpperCase()).join(", ")})
                  </p>
                  <div className="flex gap-1">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              const cssVars = palette
                                .map(
                                  (color, index) =>
                                    `  --color-${index + 1}: ${color.hex};`,
                                )
                                .join("\n");

                              const css = `:root {\n${cssVars}\n}`;
                              navigator.clipboard.writeText(css);
                              setCopiedMsg("CSS copiado al portapapeles");
                              setTimeout(() => setCopiedMsg(null), 2000);
                            }}
                          >
                            <Palette className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Copiar como CSS</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              const json = JSON.stringify(
                                palette.map((color) => ({
                                  hex: color.hex,
                                  name: color.name,
                                })),
                                null,
                                2,
                              );
                              navigator.clipboard.writeText(json);
                              setCopiedMsg("JSON copiado al portapapeles");
                              setTimeout(() => setCopiedMsg(null), 2000);
                            }}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Copiar como JSON</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Notificación de copiado */}
      {copiedMsg && (
        <motion.div
          className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl px-5 py-4 border border-green-100 dark:border-green-900 z-50"
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-full bg-green-50 dark:bg-green-900/30 flex items-center justify-center">
              <Check className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="font-medium text-gray-800 dark:text-gray-200">
                {copiedMsg}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Puedes pegar en cualquier aplicación
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Información adicional */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Info className="w-5 h-5 mr-2" />
            Cómo usar esta herramienta
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-gray-700">
            <p>
              <strong>1.</strong> Selecciona el tipo de paleta que deseas
              generar.
            </p>
            <p>
              <strong>2.</strong> Ajusta el número de colores según tus
              necesidades.
            </p>
            <p>
              <strong>3.</strong> Puedes bloquear colores específicos que
              quieras mantener presionando el icono de candado.
            </p>
            <p>
              <strong>4.</strong> Genera nuevas variaciones sin perder los
              colores bloqueados.
            </p>
            <p>
              <strong>5.</strong> Para extraer una paleta de una imagen, usa el
              botón "Extraer de imagen".
            </p>
            <p>
              <strong>6.</strong> Exporta tu paleta en formato CSS o JSON, o
              guárdala para usar más tarde.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
