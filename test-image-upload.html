<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Visual Complexity Analyzer Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #results { margin-top: 20px; }
        .log-entry { 
            margin: 5px 0; 
            padding: 5px; 
            font-family: monospace; 
            font-size: 12px;
            border-left: 3px solid #007bff;
            background-color: #f8f9fa;
        }
        canvas { border: 1px solid #ddd; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Visual Complexity Analyzer - Image Upload Test</h1>
        <p>This test will verify that the image upload fix is working correctly.</p>

        <div class="test-section info">
            <h3>📋 Test Steps</h3>
            <ol>
                <li>Create a test image with design elements</li>
                <li>Send it to the backend for analysis</li>
                <li>Verify the image is uploaded to Supabase Storage</li>
                <li>Check that file_url is properly set in the database</li>
                <li>Test image retrieval from storage</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎨 Test Image Generation</h3>
            <canvas id="testCanvas" width="400" height="300"></canvas>
            <br>
            <button onclick="generateTestImage()">Generate Test Image</button>
            <button onclick="runFullTest()" id="testBtn" disabled>Run Full Test</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let testImageBlob = null;

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(entry);
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function generateTestImage() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Create a complex design for testing
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#3498db');
            gradient.addColorStop(0.5, '#9b59b6');
            gradient.addColorStop(1, '#e74c3c');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Add some geometric shapes
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(50, 50, 100, 100);
            
            ctx.fillStyle = '#2ecc71';
            ctx.beginPath();
            ctx.arc(300, 100, 50, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#f39c12';
            ctx.beginPath();
            ctx.moveTo(200, 200);
            ctx.lineTo(250, 150);
            ctx.lineTo(300, 200);
            ctx.lineTo(250, 250);
            ctx.closePath();
            ctx.fill();
            
            // Add text
            ctx.fillStyle = '#ffffff';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Test Design', canvas.width / 2, canvas.height - 30);
            
            // Convert to blob
            canvas.toBlob((blob) => {
                testImageBlob = blob;
                log('✅ Test image generated successfully', 'success');
                document.getElementById('testBtn').disabled = false;
            }, 'image/png');
        }

        async function runFullTest() {
            if (!testImageBlob) {
                log('❌ No test image available. Generate one first.', 'error');
                return;
            }

            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';

            try {
                log('🚀 Starting Visual Complexity Analyzer test...', 'info');
                
                // Step 1: Check authentication
                log('🔐 Checking authentication...', 'info');
                const response = await fetch('/api/auth/session');
                if (!response.ok) {
                    log('❌ Not authenticated. Please log in to Emma Studio first.', 'error');
                    return;
                }
                const session = await response.json();
                log(`✅ Authenticated as user: ${session.user?.email || 'Unknown'}`, 'success');

                // Step 2: Send analysis request
                log('📤 Sending analysis request to backend...', 'info');
                const formData = new FormData();
                const testFile = new File([testImageBlob], 'test-design.png', { type: 'image/png' });
                formData.append('design', testFile);

                const analysisResponse = await fetch('/api/analyze-design', {
                    method: 'POST',
                    body: formData
                });

                log(`📥 Analysis response: ${analysisResponse.status} ${analysisResponse.statusText}`, 
                    analysisResponse.ok ? 'success' : 'error');

                if (!analysisResponse.ok) {
                    const errorText = await analysisResponse.text();
                    log(`❌ Analysis failed: ${errorText}`, 'error');
                    return;
                }

                const analysisData = await analysisResponse.json();
                log('✅ Analysis completed successfully', 'success');
                log(`📊 Analysis ID: ${analysisData.analysis_id}`, 'info');
                log(`💾 Saved to database: ${analysisData.saved_to_database}`, 'info');
                log(`🖼️ File URL: ${analysisData.file_url || 'NULL'}`, 
                    analysisData.file_url ? 'success' : 'error');

                // Step 3: Verify database record
                if (analysisData.analysis_id) {
                    log('🔍 Verifying database record...', 'info');
                    const dbResponse = await fetch(`/api/design-analyses/${analysisData.analysis_id}`);
                    
                    if (dbResponse.ok) {
                        const dbRecord = await dbResponse.json();
                        log('✅ Database record retrieved successfully', 'success');
                        log(`📁 Original filename: ${dbRecord.original_filename}`, 'info');
                        log(`🔗 File URL in DB: ${dbRecord.file_url || 'NULL'}`, 
                            dbRecord.file_url ? 'success' : 'error');
                        log(`📈 Overall score: ${dbRecord.overall_score}/100`, 'info');

                        // Step 4: Test image retrieval if file_url exists
                        if (dbRecord.file_url) {
                            log('🖼️ Testing image retrieval...', 'info');
                            try {
                                const imageResponse = await fetch(`/api/design-analyses/${analysisData.analysis_id}/image`);
                                if (imageResponse.ok) {
                                    const imageBlob = await imageResponse.blob();
                                    log(`✅ Image retrieved successfully (${imageBlob.size} bytes)`, 'success');
                                    
                                    // Create a preview
                                    const imageUrl = URL.createObjectURL(imageBlob);
                                    const img = document.createElement('img');
                                    img.src = imageUrl;
                                    img.style.maxWidth = '200px';
                                    img.style.border = '1px solid #ddd';
                                    img.style.margin = '10px 0';
                                    
                                    const preview = document.createElement('div');
                                    preview.className = 'test-section success';
                                    preview.innerHTML = '<h4>🖼️ Retrieved Image Preview:</h4>';
                                    preview.appendChild(img);
                                    document.getElementById('results').appendChild(preview);
                                    
                                    log('🎉 ALL TESTS PASSED! Image upload and retrieval working correctly.', 'success');
                                } else {
                                    log(`❌ Image retrieval failed: ${imageResponse.status}`, 'error');
                                }
                            } catch (imageError) {
                                log(`❌ Image retrieval error: ${imageError.message}`, 'error');
                            }
                        } else {
                            log('❌ CRITICAL: file_url is NULL - image upload failed!', 'error');
                        }
                    } else {
                        log('❌ Failed to retrieve database record', 'error');
                    }
                }

            } catch (error) {
                log(`💥 Test failed with error: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = 'Run Full Test';
            }
        }

        // Auto-generate test image on page load
        window.onload = () => {
            generateTestImage();
        };
    </script>
</body>
</html>
