version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.poetry
    container_name: plataforma-backend-poetry
    restart: unless-stopped
    environment:
      - PORT=8001
      - HOST=0.0.0.0
      - DATABASE_URL=${DATABASE_URL:-**************************************/plataforma}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - STABILITY_API_KEY=${STABILITY_API_KEY:-}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    ports:
      - "8001:8001"
    depends_on:
      - db
    networks:
      - plataforma-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # Set reasonable resource limits
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  db:
    image: postgres:16-alpine
    container_name: plataforma-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-plataforma}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - plataforma-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    # Set reasonable resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 256M

  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: plataforma-client
    restart: unless-stopped
    ports:
      - "3000:80"
    networks:
      - plataforma-network
    depends_on:
      - backend
    # Set reasonable resource limits
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

networks:
  plataforma-network:
    driver: bridge

volumes:
  postgres_data: