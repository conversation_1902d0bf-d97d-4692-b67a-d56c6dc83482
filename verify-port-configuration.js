/**
 * Verification script to check port configuration across Emma Studio
 * This script checks that all configuration files are using port 8001 consistently
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Emma Studio Port Configuration Verification');
console.log('==============================================\n');

// Files to check for port configuration
const CONFIG_FILES = [
  {
    path: 'client/vite.config.ts',
    description: 'Vite proxy configuration',
    expectedPort: '8001',
    patterns: [/target:\s*['"]http:\/\/127\.0\.0\.1:(\d+)['"]/]
  },
  {
    path: 'backend/app/core/config.py',
    description: 'Backend configuration',
    expectedPort: '8001',
    patterns: [/PORT:\s*int\s*=\s*(\d+)/]
  },
  {
    path: 'start_backend.bat',
    description: 'Backend startup script',
    expectedPort: '8001',
    patterns: [/--port\s+(\d+)/g]
  },
  {
    path: 'docker-compose.yml',
    description: 'Docker compose configuration',
    expectedPort: '8001',
    patterns: [/"(\d+):(\d+)"/g, /localhost:(\d+)\/health/]
  },
  {
    path: 'backend/Dockerfile',
    description: 'Backend Dockerfile',
    expectedPort: '8001',
    patterns: [/EXPOSE\s+(\d+)/, /--port.*?(\d+)/, /localhost:(\d+)/]
  }
];

function checkFile(fileConfig) {
  const filePath = path.join(process.cwd(), fileConfig.path);
  
  console.log(`📄 Checking ${fileConfig.description}`);
  console.log(`   File: ${fileConfig.path}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ❌ File not found`);
    return { success: false, reason: 'File not found' };
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    const foundPorts = new Set();
    
    for (const pattern of fileConfig.patterns) {
      const matches = content.matchAll(pattern);
      
      for (const match of matches) {
        // Extract port number from match
        const portMatch = match[1] || match[2]; // Handle different capture groups
        if (portMatch && /^\d+$/.test(portMatch)) {
          foundPorts.add(portMatch);
          
          if (portMatch !== fileConfig.expectedPort) {
            issues.push(`Found port ${portMatch}, expected ${fileConfig.expectedPort}`);
          }
        }
      }
    }
    
    if (issues.length === 0 && foundPorts.size > 0) {
      console.log(`   ✅ Correct port configuration (${Array.from(foundPorts).join(', ')})`);
      return { success: true, ports: Array.from(foundPorts) };
    } else if (foundPorts.size === 0) {
      console.log(`   ⚠️ No port configuration found`);
      return { success: false, reason: 'No port configuration found' };
    } else {
      console.log(`   ❌ Port configuration issues:`);
      issues.forEach(issue => console.log(`      - ${issue}`));
      return { success: false, reason: 'Incorrect port configuration', issues, ports: Array.from(foundPorts) };
    }
    
  } catch (error) {
    console.log(`   ❌ Error reading file: ${error.message}`);
    return { success: false, reason: `Error reading file: ${error.message}` };
  }
}

function verifyPortConfiguration() {
  console.log('🔍 Checking port configuration in all files...\n');
  
  const results = [];
  
  for (const fileConfig of CONFIG_FILES) {
    const result = checkFile(fileConfig);
    results.push({
      file: fileConfig.path,
      description: fileConfig.description,
      ...result
    });
    console.log(''); // Add spacing
  }
  
  // Summary
  console.log('📊 Verification Summary:');
  console.log('========================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Correctly configured: ${successful.length}/${results.length}`);
  console.log(`❌ Issues found: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ Files with correct configuration:');
    successful.forEach(r => {
      console.log(`   - ${r.description} (${r.file})`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Files with issues:');
    failed.forEach(r => {
      console.log(`   - ${r.description} (${r.file}): ${r.reason}`);
      if (r.issues) {
        r.issues.forEach(issue => console.log(`     * ${issue}`));
      }
    });
  }
  
  // Overall status
  console.log('\n🎯 Overall Status:');
  console.log('==================');
  
  if (failed.length === 0) {
    console.log('🎉 All configuration files are correctly set to use port 8001!');
    console.log('\nNext steps:');
    console.log('1. Start the backend: cd backend && python -m uvicorn app.main:app --reload --port 8001');
    console.log('2. Start the frontend: cd client && npm run dev');
    console.log('3. Access Emma Studio: http://localhost:3002');
    console.log('4. Test mood board tool: http://localhost:3002/dashboard/herramientas/mood-board');
  } else {
    console.log('⚠️ Some configuration files need to be updated to use port 8001.');
    console.log('\nPlease fix the issues above and run this script again.');
  }
  
  return results;
}

// Additional checks
function checkProcesses() {
  console.log('\n🔍 Checking running processes...');
  
  const { exec } = require('child_process');
  
  // Check if anything is running on port 8001
  exec('netstat -an | findstr :8001', (error, stdout, stderr) => {
    if (stdout) {
      console.log('✅ Port 8001 is in use:');
      console.log(stdout);
    } else {
      console.log('ℹ️ Port 8001 is available (backend not running)');
    }
  });
  
  // Check if anything is running on port 3002
  exec('netstat -an | findstr :3002', (error, stdout, stderr) => {
    if (stdout) {
      console.log('✅ Port 3002 is in use:');
      console.log(stdout);
    } else {
      console.log('ℹ️ Port 3002 is available (frontend not running)');
    }
  });
}

// Run verification
if (require.main === module) {
  verifyPortConfiguration();
  checkProcesses();
}

module.exports = { verifyPortConfiguration, checkFile };
