services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/app:z
    environment:
      - DEBUG=true
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]

  client:
    build:
      context: .
      dockerfile: client/Dockerfile
      target: dev
    ports:
      - "3001:3000"
    volumes:
      - ./client:/app/client:z
      - ./shared:/app/shared:z
      # Ensure node_modules is not overwritten by the volume mount
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
