version: '3.8'

# Development overrides for docker-compose.poetry.yml
# This file adds development-specific settings

services:
  backend:
    build:
      dockerfile: Dockerfile.poetry.dev
    volumes:
      # Mount code for hot-reloading
      - ./backend:/app
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=debug
    # Override command to enable hot reloading
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
    # Remove resource limits for development
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  client:
    build:
      dockerfile: Dockerfile.dev
    volumes:
      # Mount code for hot-reloading
      - ./client:/app
      # Use node_modules volume for performance
      - node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:8001
    # Override command for development server
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
    # Override ports to expose Vite dev server
    ports:
      - "3000:3000"
    # Remove resource limits for development
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.2'
          memory: 512M

volumes:
  node_modules: