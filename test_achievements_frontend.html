<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Achievements Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .achievement { background: #f0f9ff; border: 1px solid #0ea5e9; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #fef2f2; border: 1px solid #ef4444; color: #dc2626; }
        .success { background: #f0fdf4; border: 1px solid #22c55e; color: #16a34a; }
        button { background: #3b82f6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #2563eb; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🏆 Test de Achievements Frontend</h1>
    <p>Esta página prueba que el frontend de Emma esté recibiendo correctamente los achievements del backend.</p>
    
    <button onclick="testAchievements()">🚀 Probar Achievements</button>
    
    <div id="results"></div>

    <script>
        async function testAchievements() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>🔄 Analizando...</p>';
            
            try {
                console.log('🚀 Iniciando test de achievements...');
                
                const response = await fetch('http://localhost:8001/api/seo/analyze', {  // TODO EL BACKEND CORRE EN PUERTO 8001
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: 'https://ambrosia.mx/2025/06/05/tipos-de-banquetes-y-cual-elegir/'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('📊 Datos completos:', data);
                console.log('🏆 Achievements:', data.achievements);
                
                let html = '<h2>✅ Resultados del Test</h2>';
                
                // Verificar que achievements existe
                if (data.achievements) {
                    html += `<div class="success">
                        <h3>🎉 ¡Achievements encontrados!</h3>
                        <p><strong>Cantidad:</strong> ${data.achievements.length}</p>
                    </div>`;
                    
                    // Mostrar cada achievement
                    data.achievements.forEach((ach, index) => {
                        html += `<div class="achievement">
                            <h4>${ach.icon} ${ach.achievement}</h4>
                            <p><strong>Categoría:</strong> ${ach.category}</p>
                            <p><strong>Descripción:</strong> ${ach.description}</p>
                            <p><strong>Impacto:</strong> ${ach.impact}</p>
                        </div>`;
                    });
                } else {
                    html += `<div class="error">
                        <h3>❌ No se encontraron achievements</h3>
                        <p>El campo 'achievements' no existe en la respuesta del backend.</p>
                    </div>`;
                }
                
                // Mostrar estructura de datos
                html += `<div style="margin-top: 20px;">
                    <h3>📋 Estructura de Datos</h3>
                    <p><strong>Keys en respuesta:</strong> ${Object.keys(data).join(', ')}</p>
                    <p><strong>Status:</strong> ${data.status}</p>
                    <p><strong>URL:</strong> ${data.url}</p>
                    <p><strong>AI Enhanced:</strong> ${data.ai_enhanced}</p>
                </div>`;
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                console.error('❌ Error en test:', error);
                resultsDiv.innerHTML = `<div class="error">
                    <h3>❌ Error en el Test</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Verifica que el backend esté corriendo en http://localhost:8000</p>
                </div>`;
            }
        }
        
        // Auto-ejecutar el test al cargar la página
        window.onload = function() {
            console.log('🏆 Página de test cargada. Haz clic en el botón para probar.');
        };
    </script>
</body>
</html>
