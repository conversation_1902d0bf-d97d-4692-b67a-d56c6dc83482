/**
 * Test script to verify mood board API endpoints are working correctly
 * Run this in the browser console after starting the backend on port 8001
 */

console.log('🧪 Testing Mood Board API Endpoints');
console.log('===================================\n');

const API_BASE_URL = 'http://localhost:8001';

// Test configuration
const TEST_CONFIG = {
  endpoints: [
    { path: '/health', method: 'GET', description: 'Health check' },
    { path: '/api/moodboard/list', method: 'GET', description: 'List mood boards', requiresAuth: true },
    { path: '/api/moodboard/stats/summary', method: 'GET', description: 'Mood board stats', requiresAuth: true }
  ]
};

async function testEndpoint(endpoint) {
  const url = `${API_BASE_URL}${endpoint.path}`;
  console.log(`🔍 Testing ${endpoint.method} ${endpoint.path}`);
  console.log(`   Description: ${endpoint.description}`);
  
  try {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    // Add auth token if required
    if (endpoint.requiresAuth) {
      const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        console.log(`   ✅ Auth token found and added`);
      } else {
        console.log(`   ⚠️ No auth token found - this may fail`);
      }
    }
    
    const response = await fetch(url, {
      method: endpoint.method,
      headers
    });
    
    console.log(`   📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Success!`);
      
      // Log specific details based on endpoint
      if (endpoint.path === '/health') {
        console.log(`   📋 Health status: ${data.status || 'OK'}`);
      } else if (endpoint.path === '/api/moodboard/list') {
        console.log(`   📋 Mood boards found: ${data.moodboards?.length || 0}`);
        console.log(`   📋 Total count: ${data.total_count || 0}`);
      } else if (endpoint.path === '/api/moodboard/stats/summary') {
        console.log(`   📋 Total mood boards: ${data.total_moodboards || 0}`);
        console.log(`   📋 Active mood boards: ${data.active_moodboards || 0}`);
        console.log(`   📋 Favorite mood boards: ${data.favorite_moodboards || 0}`);
      }
      
      return { success: true, status: response.status, data };
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Failed: ${errorText}`);
      return { success: false, status: response.status, error: errorText };
    }
    
  } catch (error) {
    console.log(`   ❌ Network error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testBackendConnectivity() {
  console.log('🌐 Testing Backend Connectivity...\n');
  
  const results = [];
  
  for (const endpoint of TEST_CONFIG.endpoints) {
    const result = await testEndpoint(endpoint);
    results.push({ endpoint: endpoint.path, ...result });
    console.log(''); // Add spacing between tests
  }
  
  // Summary
  console.log('📊 Test Results Summary:');
  console.log('========================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ Successful endpoints:');
    successful.forEach(r => {
      console.log(`   - ${r.endpoint} (${r.status})`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed endpoints:');
    failed.forEach(r => {
      console.log(`   - ${r.endpoint} (${r.status || 'Network Error'}): ${r.error}`);
    });
  }
  
  // Specific diagnostics
  console.log('\n🔧 Diagnostics:');
  console.log('===============');
  
  const healthResult = results.find(r => r.endpoint === '/health');
  if (healthResult?.success) {
    console.log('✅ Backend is running and accessible on port 8001');
  } else {
    console.log('❌ Backend is not accessible on port 8001');
    console.log('   - Check if backend is running');
    console.log('   - Verify port configuration (should be 8001)');
    console.log('   - Check for firewall or network issues');
  }
  
  const authEndpoints = results.filter(r => r.endpoint.includes('/api/moodboard'));
  const authSuccessful = authEndpoints.filter(r => r.success);
  const authFailed = authEndpoints.filter(r => !r.success);
  
  if (authSuccessful.length > 0) {
    console.log('✅ Mood board API endpoints are accessible');
  }
  
  if (authFailed.length > 0) {
    const hasAuthErrors = authFailed.some(r => r.status === 401 || r.status === 403);
    if (hasAuthErrors) {
      console.log('⚠️ Authentication required for mood board endpoints');
      console.log('   - Make sure you are logged in to Emma Studio');
      console.log('   - Check that auth token is stored in localStorage or sessionStorage');
    } else {
      console.log('❌ Mood board API endpoints have issues');
      console.log('   - Check backend logs for errors');
      console.log('   - Verify Supabase configuration');
    }
  }
  
  // Frontend proxy test
  console.log('\n🔄 Testing Frontend Proxy...');
  try {
    const proxyResponse = await fetch('/api/moodboard/list', {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token') || 'test'}`
      }
    });
    
    console.log(`📊 Proxy Status: ${proxyResponse.status} ${proxyResponse.statusText}`);
    
    if (proxyResponse.ok || proxyResponse.status === 401) {
      console.log('✅ Frontend proxy is working correctly');
      console.log('   - Requests to /api/* are being proxied to backend');
    } else {
      console.log('❌ Frontend proxy may have issues');
      console.log('   - Check Vite proxy configuration');
    }
  } catch (proxyError) {
    console.log(`❌ Proxy test failed: ${proxyError.message}`);
    console.log('   - Make sure you are running this from the frontend (localhost:3002)');
  }
  
  return results;
}

// Auto-run the test
testBackendConnectivity().then(results => {
  console.log('\n🎯 Next Steps:');
  console.log('==============');
  
  const allSuccessful = results.every(r => r.success || r.status === 401);
  
  if (allSuccessful) {
    console.log('🎉 All tests passed! The mood board tool should work correctly.');
    console.log('   - Try accessing http://localhost:3002/dashboard/herramientas/mood-board');
    console.log('   - Make sure you are logged in to see your mood boards');
  } else {
    console.log('🔧 Some issues found. Please:');
    console.log('   1. Make sure the backend is running on port 8001');
    console.log('   2. Check backend logs for any errors');
    console.log('   3. Verify Supabase configuration');
    console.log('   4. Ensure you are logged in to Emma Studio');
  }
});

// Export for manual testing
window.testMoodBoardAPI = testBackendConnectivity;
